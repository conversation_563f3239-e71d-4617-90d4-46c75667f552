/* WelcomePage.css - 欢迎页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Titillium+Web:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&display=swap');

/* Glass Surface Navigation Bar Styles - 全新玻璃表面效果 */
.glass-navigation {
  animation: navFadeIn 0.8s ease-out;
}

/* Floating Navigation Bar Styles - 液体玻璃效果 (保留作为备用) */
.floating-navigation {
  animation: navFadeIn 0.8s ease-out;
  /* 确保初始位置正确，防止闪烁 */
  position: fixed !important;
  top: 30px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  /* 防止下拉菜单被剪切 */
  overflow: visible;
  /* 添加若隐若现的白色光晕效果 */
}

/* 添加贴着背景的若隐若现白色光晕 */
.floating-navigation::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.06) 25%,
    rgba(255, 255, 255, 0.12) 50%,
    rgba(255, 255, 255, 0.06) 75%,
    rgba(255, 255, 255, 0.12) 100%
  );
  border-radius: 50px;
  z-index: -1;
  opacity: 0.7;
  filter: blur(1px);
  animation: subtleGlow 6s ease-in-out infinite; /* 增加动画时长，减少频率 */
}

/* 添加外层更柔和的光晕 */
.floating-navigation::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: radial-gradient(ellipse,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.03) 40%,
    transparent 70%
  );
  border-radius: 60px;
  z-index: -2;
  opacity: 0.5;
  filter: blur(2px);
  animation: subtleGlow 8s ease-in-out infinite reverse; /* 进一步增加动画时长 */
}

/* 若隐若现的光晕动画 - 优化版本，减慢频率 */
@keyframes subtleGlow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  25% {
    opacity: 0.6;
    transform: scale(1.003); /* 减少缩放幅度 */
  }
  50% {
    opacity: 0.8;
    transform: scale(1.006); /* 减少缩放幅度 */
  }
  75% {
    opacity: 0.6;
    transform: scale(1.003); /* 减少缩放幅度 */
  }
}

/* 悬停时增强光晕效果 - 优化版本 */
.floating-navigation:hover::before {
  opacity: 0.9;
  animation-duration: 3s; /* 增加悬停时的动画时长 */
}

.floating-navigation:hover::after {
  opacity: 0.7;
  animation-duration: 4s; /* 增加悬停时的动画时长 */
}

@keyframes navFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 完全移除背景，让液体玻璃效果完全负责 */
  background: transparent;
  /* 移除所有传统模糊效果 */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  /* 移除边框，让液体玻璃处理 */
  border: none;
  border-radius: 50px;
  padding: 0;
  /* 移除阴影，让液体玻璃提供深度 */
  box-shadow: none;
  transition: all 0.3s ease;
  /* 移除minWidth以防止鼠标悬停时背景拉伸 */
  position: relative;
  /* 确保容器高度固定，防止下拉菜单影响布局 */
  height: 56px;
  /* 防止下拉菜单被剪切 */
  overflow: visible;
}

/* Glass Surface 导航栏特定样式 */
.glass-navigation .nav-container {
  padding: 0 24px;
  min-width: 600px;
  border-radius: 28px;
  height: 56px;
  display: flex;
  align-items: center;
}

.glass-navigation .nav-item {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.glass-navigation .nav-item:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.glass-navigation .nav-item.active {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
  font-weight: 600;
}

.glass-navigation .auth-button {
  background: transparent; /* 移除背景 */
  border: none; /* 移除边框 */
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  padding: 10px 14px; /* 与其他导航按钮相同的内边距 */
  min-height: 36px; /* 确保最小高度 */
  display: flex;
  align-items: center;
}

.glass-navigation .auth-button:hover {
  background: rgba(255, 255, 255, 0.1); /* 与其他导航按钮相同的悬停背景 */
  color: #ffffff;
  transform: translateY(-1px);
}

/* 下拉菜单样式 */
.glass-navigation .dropdown-content {
  display: flex;
  flex-direction: column;
  padding: 8px;
  gap: 4px;
}

.glass-navigation .dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  box-sizing: border-box;

  /* 玻璃导航下拉菜单项的折射效果 */
  text-shadow:
    0 0 8px rgba(255, 255, 255, 0.2),
    0 0 15px rgba(255, 255, 255, 0.15),
    0 0 25px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.3),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.15),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  filter:
    drop-shadow(0 0 1px rgba(255, 255, 255, 0.2))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
}

.glass-navigation .dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateX(2px);

  /* 玻璃导航下拉菜单项悬停时的增强折射效果 */
  text-shadow:
    0 0 12px rgba(255, 255, 255, 0.4),
    0 0 20px rgba(255, 255, 255, 0.25),
    0 0 30px rgba(77, 200, 255, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.5),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.25),
    -1px 0 0 rgba(0, 0, 0, 0.15),
    0 0 4px rgba(77, 200, 255, 0.3);

  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.25))
    drop-shadow(0 0 6px rgba(77, 200, 255, 0.25));
}

.glass-navigation .dropdown-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: #4dc8ff;
  font-weight: 700;

  /* 玻璃导航激活状态下拉菜单项的强烈折射效果 */
  text-shadow:
    0 0 15px rgba(77, 200, 255, 0.5),
    0 0 25px rgba(77, 200, 255, 0.3),
    0 0 35px rgba(255, 255, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.6),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.3),
    -1px 0 0 rgba(0, 0, 0, 0.2),
    0 0 6px rgba(77, 200, 255, 0.4);

  filter:
    drop-shadow(0 0 3px rgba(77, 200, 255, 0.4))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(77, 200, 255, 0.3));
}

.glass-navigation .dropdown-item svg {
  opacity: 0.8;
}

.glass-navigation .dropdown-item:hover svg {
  opacity: 1;
}

/* 移除nav-container的伪元素背景效果，避免与LiquidGlass冲突 */

@keyframes navShimmer {
  0%, 90% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

.nav-container:hover {
  /* 移除所有悬停变换效果 */
  background: transparent;
  border: none;
  box-shadow: none;
  transform: none;
}

/* Navigation sections */
.nav-left {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加间距以适应中文 */
  flex: 1;
  position: relative;
  z-index: 1;
  /* 确保左侧导航不会因下拉菜单而变形 */
  height: 100%;
}

.nav-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  margin: 0 18px; /* 增加中心区域的边距 */
  position: relative;
  z-index: 1;
  /* 确保中心区域不会因下拉菜单而变形 */
  height: 100%;
}

.nav-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  position: relative;
  z-index: 1;
  /* 确保右侧导航不会因下拉菜单而变形 */
  height: 100%;
}

/* Navigation items */
.nav-item-wrapper {
  position: relative;
  /* 确保包装器不会因为子元素的下拉菜单而变形 */
  height: 100%;
  display: flex;
  align-items: center;
  /* 确保下拉菜单可以正确定位 */
  overflow: visible;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Rajdhani', sans-serif;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 10px 14px; /* 调整内边距以适应新高度 */
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: auto;
  min-height: 36px; /* 确保最小高度 */
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  z-index: 2;
  white-space: nowrap; /* 防止中文换行 */
  /* 确保导航项高度固定 */
  height: 32px;
  box-sizing: border-box;

  /* 文字折射效果 */
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.3),
    0 0 20px rgba(255, 255, 255, 0.2),
    0 0 30px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.4),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.2),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  /* 添加轻微的滤镜效果增强折射感 */
  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-item:hover {
  color: #ffffff;
  background: rgba(77, 200, 255, 0.1);
  /* 完全移除所有变换效果，包括translateY和scale */
  transform: none;

  /* 增强悬停时的折射效果 */
  text-shadow:
    0 0 15px rgba(255, 255, 255, 0.5),
    0 0 25px rgba(255, 255, 255, 0.3),
    0 0 35px rgba(77, 200, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.6),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.3),
    -1px 0 0 rgba(0, 0, 0, 0.2),
    0 0 5px rgba(77, 200, 255, 0.4);

  filter:
    drop-shadow(0 0 3px rgba(255, 255, 255, 0.4))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(77, 200, 255, 0.3));
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item.active {
  color: #4dc8ff;
  background: rgba(77, 200, 255, 0.15);
  font-weight: 700;

  /* 激活状态的强烈折射效果 */
  text-shadow:
    0 0 20px rgba(77, 200, 255, 0.6),
    0 0 30px rgba(77, 200, 255, 0.4),
    0 0 40px rgba(255, 255, 255, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.8),
    0 -1px 0 rgba(0, 0, 0, 0.4),
    1px 0 0 rgba(255, 255, 255, 0.4),
    -1px 0 0 rgba(0, 0, 0, 0.3),
    0 0 8px rgba(77, 200, 255, 0.5),
    0 2px 4px rgba(0, 0, 0, 0.2);

  filter:
    drop-shadow(0 0 5px rgba(77, 200, 255, 0.5))
    drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4))
    drop-shadow(0 0 12px rgba(77, 200, 255, 0.4));
}

.nav-item.active::before {
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.2), transparent);
}

/* 中文界面特殊样式 */
.nav-item[data-lang="zh"] {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
  font-weight: 600;
  text-transform: none; /* 中文不需要大写转换 */
}

/* 中文下拉菜单项特殊样式 */
.dropdown-item[data-lang="zh"] {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  text-transform: none;
  letter-spacing: 1px;
  font-weight: 500;
}

.auth-button[data-lang="zh"] {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 1px;
  font-weight: 600;
  text-transform: none;
}

.dropdown-icon {
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.6);
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

/* Language selector - now using nav-item styles */

/* Auth button - 修改为与其他导航按钮一致的样式 */
.auth-button {
  background: transparent; /* 移除背景渐变 */
  border: none; /* 移除边框 */
  color: rgba(255, 255, 255, 0.9); /* 与其他导航按钮相同的颜色 */
  font-family: 'Rajdhani', sans-serif;
  font-size: 13px; /* 与其他导航按钮相同的字体大小 */
  font-weight: 600; /* 与其他导航按钮相同的字体粗细 */
  padding: 10px 14px; /* 与其他导航按钮相同的内边距 */
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;

  /* 认证按钮的折射效果 */
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.3),
    0 0 20px rgba(255, 255, 255, 0.2),
    0 0 30px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.4),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.2),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  white-space: nowrap;
  z-index: 2;
  /* 确保按钮高度固定 */
  height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.auth-button:hover {
  color: #ffffff; /* 与其他导航按钮相同的悬停颜色 */
  background: rgba(77, 200, 255, 0.1); /* 与其他导航按钮相同的悬停背景 */
  /* 移除所有变换效果，包括translateY */
  transform: none;

  /* 认证按钮悬停时的增强折射效果 */
  text-shadow:
    0 0 15px rgba(255, 255, 255, 0.5),
    0 0 25px rgba(255, 255, 255, 0.3),
    0 0 35px rgba(77, 200, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.6),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.3),
    -1px 0 0 rgba(0, 0, 0, 0.2),
    0 0 5px rgba(77, 200, 255, 0.4);

  filter:
    drop-shadow(0 0 3px rgba(255, 255, 255, 0.4))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(77, 200, 255, 0.3));
}

.auth-button:hover::before {
  left: 100%;
}

/* Dropdown menus */
.dropdown-menu {
  position: fixed;
  background: rgba(1, 3, 19, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 12px;
  padding: 8px;
  min-width: 160px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  z-index: 1200;
  animation: dropdownFadeIn 0.3s ease;
  /* 确保下拉菜单不影响父容器布局 */
  pointer-events: auto;
  /* 防止被父容器截断 */
  overflow: visible;
  will-change: transform, opacity;
  /* 确保在所有容器之上 */
  isolation: isolate;
}

/* Language dropdown now uses standard dropdown-menu styles */

.dropdown-item {
  display: block;
  width: 100%;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Rajdhani', sans-serif;
  font-size: 13px;
  font-weight: 600;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  /* 确保下拉菜单项不影响父容器 */
  box-sizing: border-box;
  flex-shrink: 0;
  margin: 2px 0;
  position: relative;
  overflow: hidden;

  /* 下拉菜单项的折射效果 */
  text-shadow:
    0 0 8px rgba(255, 255, 255, 0.2),
    0 0 15px rgba(255, 255, 255, 0.15),
    0 0 25px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.3),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.15),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  filter:
    drop-shadow(0 0 1px rgba(255, 255, 255, 0.2))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.dropdown-item:hover {
  background: rgba(77, 200, 255, 0.1);
  color: #ffffff;
  /* 保留轻微的横向移动效果，但移除任何缩放效果 */
  transform: translateX(2px);

  /* 下拉菜单项悬停时的增强折射效果 */
  text-shadow:
    0 0 12px rgba(255, 255, 255, 0.4),
    0 0 20px rgba(255, 255, 255, 0.25),
    0 0 30px rgba(77, 200, 255, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.5),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.25),
    -1px 0 0 rgba(0, 0, 0, 0.15),
    0 0 4px rgba(77, 200, 255, 0.3);

  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.25))
    drop-shadow(0 0 6px rgba(77, 200, 255, 0.25));
}

.dropdown-item:hover::before {
  left: 100%;
}

.dropdown-item.active {
  background: rgba(77, 200, 255, 0.2);
  color: #4dc8ff;
  font-weight: 700;

  /* 激活状态下拉菜单项的强烈折射效果 */
  text-shadow:
    0 0 15px rgba(77, 200, 255, 0.5),
    0 0 25px rgba(77, 200, 255, 0.3),
    0 0 35px rgba(255, 255, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.6),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.3),
    -1px 0 0 rgba(0, 0, 0, 0.2),
    0 0 6px rgba(77, 200, 255, 0.4);

  filter:
    drop-shadow(0 0 3px rgba(77, 200, 255, 0.4))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(77, 200, 255, 0.3));
}

.dropdown-item.active:hover {
  background: rgba(77, 200, 255, 0.25);
  transform: translateX(0);
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltip fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Global Industrial Network 国家悬停效果 */
.country-path {
  transition: all 0.2s ease;
  cursor: pointer;
}

.country-path.hovered {
  filter: drop-shadow(0 0 8px rgba(77, 200, 255, 0.6));
}

.country-hover-glow {
  animation: countryGlow 1.5s ease-in-out infinite alternate;
}

@keyframes countryGlow {
  0% {
    opacity: 0.3;
    filter: blur(1px);
  }
  100% {
    opacity: 0.8;
    filter: blur(2px);
  }
}

/* 国家工具提示动画 */
@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(8px) scale(0.85);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 0 8px rgba(77, 200, 255, 0.8);
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
    box-shadow: 0 0 12px rgba(77, 200, 255, 1);
  }
}

.country-tooltip {
  pointer-events: none;
  z-index: 1000;
}

/* 确保工具提示容器不会被裁剪 */
.geo-visualization svg {
  overflow: visible !important;
}

.country-tooltip foreignObject {
  overflow: visible !important;
}

/* 增强国家边界的视觉效果 */
.country-path:hover {
  filter: drop-shadow(0 0 12px rgba(77, 200, 255, 0.8));
}

/* 国家路径的平滑过渡效果 */
.country-path {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.country-path.hovered {
  filter: drop-shadow(0 0 15px rgba(77, 200, 255, 0.9))
          drop-shadow(0 0 25px rgba(77, 200, 255, 0.4));
}

/* 悬停发光效果的优化 */
.country-hover-glow {
  animation: countryGlow 1.2s ease-in-out infinite alternate;
  filter: blur(1.5px);
}

/* Glass Surface 导航栏响应式调整 */
@media (max-width: 900px) {
  .glass-navigation {
    min-width: 90vw !important;
  }

  .glass-navigation .nav-container {
    min-width: auto;
    padding: 0 16px;
  }
}

/* 中文界面响应式调整 */
@media (max-width: 900px) {
  .nav-container[data-lang="zh"] {
    min-width: 680px; /* 中文界面需要更多空间 */
  }

  .glass-navigation .nav-container[data-lang="zh"] {
    min-width: auto; /* Glass Surface 版本覆盖 */
  }
}

/* Glass Surface 移动端响应式设计 */
@media (max-width: 768px) {
  .glass-navigation {
    width: 95vw !important;
    min-width: auto !important;
    top: 10px !important;
  }

  .glass-navigation .nav-container {
    min-width: auto;
    width: 100%;
    padding: 10px 16px;
    flex-direction: column;
    gap: 12px;
    height: auto;
  }

  .glass-navigation .nav-left {
    order: 2;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .glass-navigation .nav-center {
    order: 1;
  }

  .glass-navigation .nav-right {
    order: 3;
  }

  .glass-navigation .nav-item {
    font-size: 12px;
    padding: 6px 12px;
  }

  .glass-navigation .auth-button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .nav-container {
    min-width: auto;
    width: 95vw;
    padding: 10px 16px;
    flex-direction: column;
    gap: 12px;
    /* 移动设备上允许高度自适应 */
    height: auto;
    min-height: auto;
    max-height: none;
  }

  .nav-left,
  .nav-center,
  .nav-right {
    flex: none;
    margin: 0;
    /* 移动设备上允许高度自适应 */
    height: auto;
  }

  .nav-left {
    order: 2;
    gap: 4px;
  }

  .nav-center {
    order: 1;
  }

  .nav-right {
    order: 3;
  }

  .nav-item {
    font-size: 12px;
    padding: 6px 12px;
  }

  .auth-button {
    font-size: 12px;
    padding: 6px 12px;
  }

  /* 移动端内容区域调整 */
  .content-section {
    padding: 140px 20px 0 20px; /* 移动端减少顶部padding */
  }

  .title-section {
    margin-top: 80px; /* 移动端减少顶部边距 */
  }
}

@media (max-width: 480px) {
  .glass-navigation {
    width: 98vw !important;
    top: 5px !important;
  }

  .glass-navigation .nav-container {
    padding: 8px 12px;
  }

  .glass-navigation .nav-item {
    font-size: 11px;
    padding: 5px 10px;
  }

  .glass-navigation .auth-button {
    font-size: 11px;
    padding: 5px 10px;
  }

  .floating-navigation {
    top: 10px;
  }

  .nav-container {
    padding: 8px 12px;
  }

  .nav-item {
    font-size: 11px;
    padding: 5px 10px;
  }

  .auth-button {
    font-size: 11px;
    padding: 5px 10px;
  }

  /* 小屏幕内容区域调整 */
  .content-section {
    padding: 120px 15px 0 15px; /* 小屏幕进一步减少padding */
  }

  .title-section {
    margin-top: 60px; /* 小屏幕进一步减少顶部边距 */
  }
}

/* Features Section - 重构版：优化间距和过渡 */
.features-section {
  position: relative;
  min-height: auto; /* 让内容决定高度 */
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 0 60px 0; /* 恢复标准padding，直接衔接 */
  background: transparent; /* 移除背景，使用全局背景 */
  overflow: hidden;
  z-index: 20;
  scroll-snap-align: start;
  border: none;
}

/* Section 过渡元素 - 已移除，实现直接衔接 */

.features-container {
  max-width: 1400px; /* Increased from 1200px */
  width: 100%;
  margin: 0 auto;
  padding: 0 60px; /* Increased from 40px */
  position: relative;
}

/* 特性标题部分 - 增强科技感 */
.features-header {
  margin-bottom: 50px;
  text-align: center;
  position: relative;
  background: transparent;
}

/* 技术风格标题容器 */
.tech-title-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  overflow: hidden;
}

/* 标题装饰元素 */
.tech-title-decoration {
  display: flex;
  align-items: center;
  width: 220px;
  margin: 12px 0;
  position: relative;
}

.tech-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(0, 123, 255, 0.1),
    rgba(0, 223, 255, 0.7),
    rgba(0, 123, 255, 0.1));
  position: relative;
  overflow: hidden;
}

.tech-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 240, 255, 0.8),
    transparent);
  animation: techLineScan 5s ease-in-out infinite; /* 增加动画时长，减少频率 */
}

@keyframes techLineScan {
  0% { left: -100%; }
  100% { left: 200%; }
}

.tech-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00eaff;
  margin: 0 15px;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 234, 255, 0.7);
}

.tech-dot::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 1px solid rgba(0, 234, 255, 0.3);
  border-radius: 50%;
  animation: techDotPulse 3s ease-in-out infinite; /* 增加动画时长 */
}

@keyframes techDotPulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.3); opacity: 0.1; } /* 减少缩放幅度 */
  100% { transform: scale(1); opacity: 0.3; }
}

/* 增强科技感的标题 */
.tech-enhanced-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 3rem;
  font-weight: 600;
  letter-spacing: 3px;
  margin: 10px 0;
  padding: 0;
  color: #e0f7ff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  text-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
}

.tech-title-prefix {
  color: #4dc8ff;
  font-size: 2rem;
  margin-right: 15px;
  opacity: 0.8;
  text-shadow: 0 0 10px rgba(77, 200, 255, 0.6);
  position: relative;
  top: -2px;
}

.tech-title-letters {
  display: flex;
  position: relative;
}

.tech-letter {
  display: inline-block;
  color: #e0f7ff;
  letter-spacing: 3px;
  text-shadow: 0 0 15px rgba(0, 162, 255, 0.5);
}

/* 数字装饰元素 - 增强版 */
.tech-digital-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
}

/* 增强二进制流的效果 */
.tech-binary-stream {
  position: absolute;
  left: 5%;
  top: 20%;
  opacity: 0.7;
  transform: rotate(-30deg) scale(0.9);
  animation: binaryFade 12s infinite alternate; /* 增加动画时长，减少频率 */
  z-index: 1;
  filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.4));
}

.tech-binary-stream.right {
  right: 5%;
  left: auto;
  top: 30%;
  transform: rotate(30deg) scale(0.9);
  animation-delay: 2s;
}

/* 添加更多二进制流 */
.tech-binary-stream.top-left {
  left: 15%;
  top: 10%;
  transform: rotate(-15deg) scale(0.7);
  animation-delay: 1s;
}

.tech-binary-stream.top-right {
  right: 15%;
  left: auto;
  top: 10%;
  transform: rotate(15deg) scale(0.7);
  animation-delay: 3s;
}

.tech-binary-stream.bottom-left {
  left: 10%;
  top: 70%;
  transform: rotate(-45deg) scale(0.7);
  animation-delay: 2.5s;
}

.tech-binary-stream.bottom-right {
  right: 10%;
  left: auto;
  top: 70%;
  transform: rotate(45deg) scale(0.7);
  animation-delay: 1.5s;
}

.binary-digit {
  display: inline-block;
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  color: #00eaff;
  margin: 0 1px;
  text-shadow: 0 0 10px rgba(0, 234, 255, 0.8);
  animation: binaryPulse 2.5s infinite; /* 增加动画时长，减少频率 */
}

.binary-digit:nth-child(odd) {
  animation-delay: 0.5s;
}

.binary-digit:nth-child(3n) {
  animation-delay: 1s;
}

/* 添加特殊突出的二进制数字 */
.binary-digit.highlight {
  font-size: 18px;
  color: #ffffff;
  text-shadow: 0 0 15px rgba(0, 234, 255, 1);
  animation: binaryHighlightPulse 3.5s infinite; /* 增加动画时长 */
}

@keyframes binaryHighlightPulse {
  0%, 100% { opacity: 0.8; text-shadow: 0 0 15px rgba(0, 234, 255, 0.9); }
  50% { opacity: 1; text-shadow: 0 0 20px rgba(0, 234, 255, 1); }
}

@keyframes binaryPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes binaryFade {
  0% { opacity: 0.4; transform: translateY(0) rotate(-30deg) scale(0.9); filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.3)); }
  100% { opacity: 0.8; transform: translateY(-15px) rotate(-30deg) scale(0.9); filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.5)); }
}

.tech-binary-stream.right {
  animation-name: binaryFadeRight;
}

@keyframes binaryFadeRight {
  0% { opacity: 0.4; transform: translateY(0) rotate(30deg) scale(0.9); filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.3)); }
  100% { opacity: 0.8; transform: translateY(-15px) rotate(30deg) scale(0.9); filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.5)); }
}

/* 添加浮动数字元素 */
.floating-digits {
  position: absolute;
  color: #00eaff;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 8px rgba(0, 234, 255, 0.7);
  opacity: 0.7;
  animation: floatingDigitAnimation 12s ease-in-out infinite; /* 增加动画时长 */
  z-index: -1;
  filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.3));
}

@keyframes floatingDigitAnimation {
  0% { transform: translateY(0) rotate(0deg); opacity: 0.4; filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2)); }
  50% { transform: translateY(-15px) rotate(5deg); opacity: 0.9; filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.4)); }
  100% { transform: translateY(0) rotate(0deg); opacity: 0.4; filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2)); }
}

/* 技术感十足的副标题 */
.features-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 auto;
  max-width: 800px;
  letter-spacing: 0.5px;
  line-height: 1.6;
  position: relative;
  opacity: 0;
  animation: subtitleFadeIn 1s ease-out forwards 0.5s;
}

@keyframes subtitleFadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* 背景粒子效果 */
.features-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  opacity: 0.6;
}

.feature-particle {
  position: absolute;
  background: rgba(74, 158, 255, 0.5);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(74, 158, 255, 0.4);
  animation: particleFloat linear infinite;
}

@keyframes particleFloat {
  0% { transform: translateY(0) translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(10px); opacity: 0; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tech-enhanced-title {
    font-size: 2.2rem;
  }

  .tech-title-prefix {
    font-size: 1.5rem;
    margin-right: 10px;
  }

  .tech-title-decoration {
    width: 180px;
  }

  .features-subtitle {
    font-size: 1rem;
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .tech-enhanced-title {
    font-size: 1.8rem;
  }

  .tech-title-prefix {
    font-size: 1.2rem;
    margin-right: 8px;
  }

  .tech-title-decoration {
    width: 140px;
  }

  .tech-dot {
    width: 6px;
    height: 6px;
    margin: 0 10px;
  }
}

/* Modern grid layout with 2 rows, 4 columns */
.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 15px; /* 添加间隙替代边框 */
  margin: 0;
  position: relative;
  overflow: hidden;
  padding: 10px;
}

/* Clean feature card design */
.feature-card {
  position: relative;
  padding: 30px 25px;
  background: transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  margin: 0;
}

/* 清除以前的悬停效果 */

/* Card with border styling */
.card-with-border {
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 30, 60, 0.1);
  background: rgba(1, 3, 19, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  border: 1px solid rgba(77, 200, 255, 0.07);
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1;
}

.card-with-border:hover {
  background: rgba(1, 3, 19, 0.3);
  border-color: rgba(77, 200, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 80, 150, 0.2);
  transform: translateY(-3px);
}

/* 添加内部光晕效果 */
.card-with-border::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(77, 200, 255, 0.08), transparent 60%);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.card-with-border:hover::after {
  opacity: 1;
}

/* 设置卡片内部内容样式 */
.card-with-border .feature-icon {
  margin-bottom: 25px;
}

.card-with-border h3 {
  font-weight: 600;
  margin-bottom: 12px;
}

.card-with-border p {
  opacity: 0.8;
  line-height: 1.6;
}

/* Icon styling */
.feature-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
  background: rgba(1, 3, 19, 0.3);
  border: 1px solid rgba(77, 200, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 100, 255, 0.1);
}

.feature-card:hover .feature-icon {
  background: rgba(1, 3, 19, 0.4);
  border-color: rgba(77, 200, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 120, 255, 0.2);
  transform: translateY(-2px);
}

.feature-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(77, 200, 255, 0.2), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  pointer-events: none;
}

.feature-card:hover .feature-icon::after {
  opacity: 1;
}

.feature-icon svg {
  width: 24px;
  height: 24px;
  color: #4dc8ff;
  filter: drop-shadow(0 0 2px rgba(77, 200, 255, 0.5));
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.feature-card:hover .feature-icon svg {
  color: #7ddfff;
  filter: drop-shadow(0 0 5px rgba(77, 200, 255, 0.8));
  transform: scale(1.1);
}

/* Feature content layout */
.feature-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.feature-card h3 {
  color: #ffffff;
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-family: 'Titillium Web', sans-serif;
  font-weight: 600;
  text-align: left;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  text-align: left;
  margin: 0;
  font-family: 'Titillium Web', sans-serif;
}

/* Animation for feature cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
.feature-card:nth-child(7) { animation-delay: 0.7s; }
.feature-card:nth-child(8) { animation-delay: 0.8s; }

/* Responsive styles */
@media (max-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 50px 0;
  }

  .features-container {
    padding: 0 20px;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .feature-card h3 {
    font-size: 1.1rem;
  }

  .feature-card p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 5px;
  }

  .feature-card {
    flex-direction: row;
    align-items: center;
    padding: 25px 20px;
    margin: 0;
  }

  .card-with-border {
    border-radius: 6px;
  }

  .feature-icon {
    margin-bottom: 0;
    margin-right: 15px;
  }

  .feature-content {
    flex: 1;
  }
}

/* Animation for feature cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
.feature-card:nth-child(7) { animation-delay: 0.7s; }
.feature-card:nth-child(8) { animation-delay: 0.8s; }

/* 流星效果 */
.meteor-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh; /* 限制在视口高度内 */
  overflow: hidden;
  pointer-events: none;
  z-index: 1.5; /* 在星空之上，地球之下 */
}

.meteor {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  top: 10%;
  left: 50%;
  z-index: 10;
  transform-origin: top center;
}

.meteor::before {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 30%, rgba(255, 255, 255, 0.3) 60%, transparent);
  transform: translateY(-1px) rotate(0deg);
  transform-origin: left center;
  animation: meteorTail 3s ease-out forwards;
  opacity: 0;
}

/* 流星头部 */
.meteor::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 50%;
  box-shadow: 0 0 2px 1px rgba(255, 255, 255, 0.8),
              0 0 4px 2px rgba(200, 255, 255, 0.6),
              0 0 6px 3px rgba(170, 220, 255, 0.4);
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  animation: meteorHead 3s ease-out forwards;
}

/* 基础流星动画 - 头部先消失，尾部渐变消失 */
@keyframes shootStarLeftArc {
  0% {
    opacity: 0;
    transform: translate(0, 0) rotate(-35deg);
  }
  5% {
    opacity: 1;
  }
  30% {
    opacity: 1;
  }
  70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate(-800px, 400px) rotate(-35deg);
  }
}

/* 流星尾部动画 - 实现头部先消失，尾部渐变消失的效果 */
@keyframes meteorTail {
  0%, 30% {
    width: 170px;
    opacity: 1;
  }
  70% {
    width: 170px;
    opacity: 1;
  }
  90% {
    width: 20px;
    opacity: 0.3;
  }
  100% {
    width: 0;
    opacity: 0;
  }
}

/* 流星头部动画 */
@keyframes meteorHead {
  0%, 70% {
    opacity: 1;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes shootStarLeftArc2 {
  0% {
    opacity: 0;
    transform: translate(0, 0) rotate(-40deg);
  }
  5% {
    opacity: 1;
  }
  30% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate(-900px, 350px) rotate(-40deg);
  }
}

/* 为每个流星配置不同的动画参数 - 优化版本，减少频率 */
.meteor-1 {
  animation: shootStarLeftArc 5s ease-out infinite; /* 增加动画时长，减少频率 */
  animation-delay: 0s;
}

.meteor-1::before {
  animation: meteorTail 5s ease-out infinite; /* 增加动画时长 */
  animation-delay: 0s;
}

.meteor-1::after {
  animation: meteorHead 5s ease-out infinite; /* 增加动画时长 */
  animation-delay: 0s;
}

.meteor-1::before {
  width: 170px;
  height: 2px;
}

.meteor-2 {
  top: 20%;
  left: 60%;
  animation: shootStarLeftArc2 6s ease-out infinite; /* 增加动画时长 */
  animation-delay: 12s; /* 增加延迟，减少同时出现的流星 */
}

.meteor-2::before {
  animation: meteorTail 6s ease-out infinite; /* 增加动画时长 */
  animation-delay: 12s;
}

.meteor-2::after {
  animation: meteorHead 6s ease-out infinite; /* 增加动画时长 */
  animation-delay: 12s;
}

.meteor-2::before {
  width: 150px;
  height: 1.5px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 20%, rgba(255, 255, 255, 0.2) 50%, transparent);
}

.meteor-3 {
  top: 15%;
  left: 40%;
  animation: shootStarLeftArc 7s ease-out infinite; /* 增加动画时长 */
  animation-delay: 20s; /* 增加延迟 */
}

.meteor-3::before {
  animation: meteorTail 7s ease-out infinite; /* 增加动画时长 */
  animation-delay: 20s;
}

.meteor-3::after {
  animation: meteorHead 7s ease-out infinite; /* 增加动画时长 */
  animation-delay: 20s;
}

.meteor-3::before {
  width: 190px;
  height: 2.5px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.8) 20%, rgba(200, 230, 255, 0.4) 60%, transparent);
}

.meteor-3::after {
  width: 5px;
  height: 5px;
}

.meteor-4 {
  top: 10%;
  left: 55%;
  animation: shootStarLeftArc2 6.5s ease-out infinite; /* 增加动画时长 */
  animation-delay: 30s; /* 增加延迟 */
}

.meteor-4::before {
  animation: meteorTail 6.5s ease-out infinite; /* 增加动画时长 */
  animation-delay: 30s;
}

.meteor-4::after {
  animation: meteorHead 6.5s ease-out infinite; /* 增加动画时长 */
  animation-delay: 30s;
}

.meteor-4::before {
  width: 140px;
  height: 1.8px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 30%, rgba(200, 220, 255, 0.3) 70%, transparent);
}

/* 重构后的统一背景系统 - 修复闪烁问题 */
.welcome-page {
  width: 100%;
  min-height: 100vh;
  color: white;
  overflow-x: hidden;
  position: relative;
  scroll-behavior: smooth;
  scroll-snap-type: y mandatory;
  /* 立即设置背景色，防止闪烁 */
  background-color: #010313 !important;
}

/* 全局统一背景 - 固定在页面底层，简化设置 */
.welcome-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 直接使用纯色背景，避免不必要的渐变 */
  background-color: #010313;
  z-index: -100;
  pointer-events: none;
}

/* 移除动态光效层以防止闪烁 - 保持纯净稳定的背景 */
.welcome-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用极其微弱的静态光效，不使用动画 */
  background:
    radial-gradient(circle at 20% 20%, rgba(77, 200, 255, 0.008) 0%, transparent 50%),
    radial-gradient(circle at 80% 40%, rgba(0, 229, 255, 0.005) 0%, transparent 50%);
  z-index: -99;
  pointer-events: none;
  /* 移除动画以防止闪烁 */
}

/* 移除重复的伪元素定义 - 已在上方统一定义 */

/* 地理智能选址主题加载屏幕样式 */
.geo-loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #010313 0%, #010313 50%, #010313 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

/* 地图网格背景 */
.map-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(77, 200, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(77, 200, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  opacity: 0.3;
}

.geo-loading-content {
  display: flex;
  align-items: center;
  gap: 80px;
  z-index: 10;
  max-width: 1200px;
  width: 100%;
  padding: 0 40px;
}

/* 雷达扫描器 */
.radar-scanner {
  position: relative;
  width: 300px;
  height: 300px;
  flex-shrink: 0;
}

.radar-circle {
  position: absolute;
  border: 2px solid;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radar-outer {
  width: 300px;
  height: 300px;
  border-color: rgba(77, 200, 255, 0.3);
  animation: radarPulse 3s ease-in-out infinite;
}

.radar-middle {
  width: 200px;
  height: 200px;
  border-color: rgba(77, 200, 255, 0.5);
  animation: radarPulse 2s ease-in-out infinite;
}

.radar-inner {
  width: 100px;
  height: 100px;
  border-color: rgba(77, 200, 255, 0.8);
  animation: radarPulse 1s ease-in-out infinite;
}

.radar-sweep {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 150px;
  height: 2px;
  background: linear-gradient(90deg, rgba(77, 200, 255, 0.8), transparent);
  transform-origin: left center;
  animation: radarSweep 3s linear infinite;
}

.radar-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  background: #4dc8ff;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(77, 200, 255, 0.8);
  animation: centerPulse 1.5s ease-in-out infinite;
}

/* 地理标记点 */
.geo-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.geo-marker {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #ff6b6b;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.8);
  animation: markerPulse 2s ease-in-out infinite;
}

.marker-1 {
  top: 25%;
  left: 30%;
  animation-delay: 0s;
}

.marker-2 {
  top: 60%;
  left: 70%;
  animation-delay: 0.5s;
}

.marker-3 {
  top: 40%;
  right: 25%;
  animation-delay: 1s;
}

.marker-4 {
  bottom: 30%;
  left: 45%;
  animation-delay: 1.5s;
}

/* 地理信息面板 */
.geo-info-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.geo-title {
  color: #ffffff;
  font-size: 36px;
  font-weight: 300;
  font-family: 'Orbitron', sans-serif;
  margin: 0;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.loading-phase {
  color: rgba(77, 200, 255, 0.9);
  font-size: 18px;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  letter-spacing: 1px;
  animation: phaseGlow 2s ease-in-out infinite;
}

/* 坐标显示 */
.coordinates-display {
  display: flex;
  gap: 40px;
  font-family: 'Roboto Mono', monospace;
}

.coord-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.coord-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 1px;
}

.coord-value {
  color: #4dc8ff;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  animation: coordFlicker 3s ease-in-out infinite;
}

/* 进度条 */
.geo-progress {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.progress-container {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4dc8ff, #00e5ff, #4dc8ff);
  border-radius: 2px;
  transition: width 0.3s ease;
  box-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-percent {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Orbitron', sans-serif;
}

.progress-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-family: 'Inter', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* 地图装饰元素 */
.map-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.map-pin {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #ff6b6b;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  animation: pinDrop 3s ease-in-out infinite;
}

.pin-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.pin-2 {
  top: 25%;
  right: 15%;
  animation-delay: 1s;
}

.pin-3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 2s;
}

.coordinate-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.coord-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.3), transparent);
  animation: lineMove 8s ease-in-out infinite;
}

.coord-line.horizontal {
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
}

.coord-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
  background: linear-gradient(0deg, transparent, rgba(77, 200, 255, 0.3), transparent);
}

/* 地图主题动画关键帧 */
@keyframes gridMove {
  0% { background-position: 0 0; }
  100% { background-position: 50px 50px; }
}

@keyframes radarPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 1;
  }
}

@keyframes radarSweep {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes centerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(77, 200, 255, 0.8);
  }
  50% {
    transform: scale(1.3);
    box-shadow: 0 0 30px rgba(77, 200, 255, 1);
  }
}

@keyframes markerPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes phaseGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(77, 200, 255, 0.8);
  }
}

@keyframes coordFlicker {
  0%, 90%, 100% { opacity: 1; }
  95% { opacity: 0.7; }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 25px rgba(77, 200, 255, 0.9);
  }
}

@keyframes pinDrop {
  0%, 100% {
    transform: rotate(-45deg) translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: rotate(-45deg) translateY(-10px);
    opacity: 1;
  }
}

@keyframes lineMove {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .geo-loading-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .radar-scanner {
    width: 250px;
    height: 250px;
  }

  .radar-outer {
    width: 250px;
    height: 250px;
  }

  .radar-middle {
    width: 170px;
    height: 170px;
  }

  .radar-inner {
    width: 90px;
    height: 90px;
  }

  .geo-title {
    font-size: 28px;
  }

  .loading-phase {
    font-size: 16px;
  }

  .coordinates-display {
    justify-content: center;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .geo-loading-content {
    padding: 0 20px;
  }

  .radar-scanner {
    width: 200px;
    height: 200px;
  }

  .radar-outer {
    width: 200px;
    height: 200px;
  }

  .radar-middle {
    width: 140px;
    height: 140px;
  }

  .radar-inner {
    width: 80px;
    height: 80px;
  }

  .geo-title {
    font-size: 24px;
    letter-spacing: 2px;
  }

  .loading-phase {
    font-size: 14px;
  }

  .coordinates-display {
    gap: 20px;
  }

  .coord-value {
    font-size: 14px;
  }
}

.loading-text-tech {
  display: inline-block;
  position: relative;
  background: linear-gradient(90deg, rgba(26, 173, 255, 0.7), rgba(100, 220, 255, 0.9), rgba(26, 173, 255, 0.7));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 0 15px rgba(0, 162, 255, 0.3);
  font-weight: 500;
  animation: textPulse 2s infinite;
  padding: 0 15px;
}

.loading-text-tech:after {
  content: '...';
  position: relative;
  display: inline-block;
  animation: dots 1.5s infinite steps(4);
  overflow: hidden;
  vertical-align: bottom;
  width: 0;
}

@keyframes dots {
  0% { width: 0; }
  100% { width: 20px; }
}

@keyframes textPulse {
  0% { opacity: 0.7; filter: blur(0.2px); }
  50% { opacity: 1; filter: blur(0); }
  100% { opacity: 0.7; filter: blur(0.2px); }
}

/* Progress bar */
.loading-progress {
  width: 240px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  margin-bottom: 30px;
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0077ff, #00c6ff, #0077ff);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(0, 162, 255, 0.7);
  animation: progressAnimate 3s ease-in-out infinite;
  background-size: 200% 100%;
}

@keyframes progressAnimate {
  0% { width: 0%; background-position: 100% 0; }
  50% { width: 70%; }
  70% { width: 70%; }
  100% { width: 100%; background-position: 0 0; }
}

/* Tech circles */
.tech-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.tech-circle {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 162, 255, 0.3);
  filter: blur(0.4px);
  opacity: 0;
  animation: circleAppear 8s infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 15%;
  animation-delay: 0.5s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 25%;
  right: 18%;
  animation-delay: 1.5s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  top: 30%;
  right: 25%;
  animation-delay: 1s;
}

.circle-4 {
  width: 120px;
  height: 120px;
  bottom: 30%;
  left: 22%;
  animation-delay: 2s;
}

.circle-5 {
  width: 60px;
  height: 60px;
  top: 15%;
  right: 30%;
  animation-delay: 0s;
}

.circle-6 {
  width: 200px;
  height: 200px;
  top: 40%;
  left: 40%;
  animation-delay: 2.5s;
}

@keyframes circleAppear {
  0% { transform: scale(0.5); opacity: 0; }
  20% { transform: scale(1); opacity: 0.6; }
  40% { transform: scale(1.1); opacity: 0.4; }
  60% { transform: scale(1); opacity: 0.7; }
  80% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(0.5); opacity: 0; }
}

/* 内容部分 - 优化间距和过渡 */
.content-section {
  width: 100%;
  min-height: 100vh; /* 改为min-height，让内容决定实际高度 */
  padding: 180px 40px 0 40px; /* 增加顶部padding，将内容往下移更多 */
  z-index: 20;
  position: relative;
  display: flex;
  align-items: flex-start; /* 改为flex-start，让内容从顶部开始 */
  justify-content: center;
  scroll-snap-align: start;
  background: transparent; /* 移除渐变背景，使用透明背景 */
}

.title-section {
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 120px; /* 增加顶部边距，进一步下移内容 */
}

.subtitle {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: #a8aabc;
  font-weight: 300;
}

.main-title {
  font-size: 6rem;
  line-height: 1;
  margin: 0;
  padding: 0;
  letter-spacing: 5px;
  font-weight: 500;
  text-transform: uppercase;
  background: linear-gradient(to right, #ffffff, #a8aabc);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.divider {
  display: none;
}

.divider-line {
  width: 100%;
  max-width: 500px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.7), transparent);
  margin: 15px 0;
}

.data-content .divider-line {
  width: 70%;
  max-width: 300px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(100, 160, 255, 0.2));
  margin: 15px 0 25px 0;
}

.description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  color: #a8aabc;
  max-width: 80%;
}

/* 公司信息 */
.company-info {
  margin-top: 30px;
  margin-bottom: 30px;
  padding: 0;
  background: transparent;
  position: relative;
  overflow: visible;
}

.company-info::before {
  display: none;
}

.company-info::after {
  display: none;
}

.company-name {
  margin-top: 0;
  font-size: 5rem;
  margin-bottom: 35px;
  letter-spacing: 2px;
  font-weight: 700;
  background: linear-gradient(135deg,
    #e8f4fd 0%,
    #b8d4f0 25%,
    #8bb8e8 50%,
    #5a9bd4 75%,
    #2e7bc6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-family: 'Cinzel', 'Playfair Display', serif;
  text-transform: none;
  position: relative;
  display: block;
  line-height: 1.2;
  text-align: center;
  z-index: 10;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.company-name::before {
  display: none;
}

.company-desc {
  margin: 0 auto 35px;
  color: #d0d4f1;
  line-height: 1.8;
  font-size: 1.15rem;
  letter-spacing: 0.6px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  font-weight: 300;
  position: relative;
  z-index: 5;
  opacity: 0;
  animation: fadeIn 1s ease-in forwards 0.5s;
  max-height: 45vh;
  overflow-y: auto;
  padding: 0 50px;
  text-align: center;
  max-width: 900px;
}

/* 优雅的艺术字体悬停效果 */
.company-name:hover {
  background: linear-gradient(135deg,
    #f0f8ff 0%,
    #d4e8f7 25%,
    #a8d0f0 50%,
    #7bb8e8 75%,
    #4ea0d9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
  transform: translateY(-2px);
}

/* 添加优雅的装饰元素 */
.company-name::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #5a9bd4, transparent);
  opacity: 0.6;
}

/* 添加科技效果的装饰元素 */
.tech-decor {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(50, 130, 252, 0.2) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.tech-decor-1 {
  width: 400px;
  height: 400px;
  top: 20%;
  left: 10%;
  opacity: 0.5;
}

.tech-decor-2 {
  width: 300px;
  height: 300px;
  bottom: 10%;
  right: 15%;
  animation-delay: 2s;
  opacity: 0.3;
}

.button-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

/* 高亮按钮样式，用于特殊强调的按钮 */
.data-cta-button .starry-button,
.explore-platform-btn {
  font-size: 1.05rem;
  padding: 14px 36px;
  border-width: 2px;
  animation: buttonGlow 3s infinite;
}

.data-cta-button .starry-button:hover,
.explore-platform-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  border-color: rgba(100, 200, 255, 0.8);
}

/* Star particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  pointer-events: none;
  animation: float var(--duration, 3s) ease-in-out var(--delay, 0s) infinite;
  opacity: 0;
  box-shadow: 0 0 5px 1px rgba(255, 255, 255, 0.8);
  width: var(--size, 2px);
  height: var(--size, 2px);
  left: var(--x, 50%);
  top: var(--y, 50%);
  transform: translate(-50%, -50%);
}

/* Button hover and active states */
.explore-platform-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  border-color: rgba(100, 200, 255, 0.8);
}

.explore-platform-btn:hover .btn-text {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
}

.explore-platform-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 100, 255, 0.4),
              inset 0 0 10px rgba(100, 200, 255, 0.3);
}

/* Animation for floating particles */
@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  20%, 80% {
    opacity: 0.8;
  }
  50% {
    transform: translate(
      calc(-50% + (var(--i) - 0.5) * 40px),
      calc(-50% - 30px)
    ) scale(1.2);
    opacity: 1;
  }
}

/* Glow effect */
.explore-platform-btn::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: rotate(0deg);
  animation: rotateGlow 8s linear infinite;
  z-index: 0;
  pointer-events: none;
}

@keyframes rotateGlow {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .explore-platform-btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .particle {
    --size: 1.5px;
  }
}

.scroll-down-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.scroll-down-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #ffffff;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 0.9rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.scroll-down-btn svg {
  transition: transform 0.3s ease;
}

.scroll-down-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.scroll-down-btn:hover svg {
  transform: translateY(3px);
}

.earth-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 10; /* 提高地球容器的z-index */
  pointer-events: none;
  overflow: hidden; /* 确保内部内容不会溢出 */
  /* 移除底部渐变过渡，避免背景层叠冲突 */
  background: transparent;
}

.earth-container canvas {
  display: block;
  width: 100%;
  height: 100%;
}

/* 为主页面内容区域添加底部过渡效果 */
.content-section::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(77, 200, 255, 0.06) 50%,
    transparent 100%
  );
  z-index: 25;
  pointer-events: none;
}

/* 团队部分 - 重构版：移除背景，专注内容 */
.team-section {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 20;
  scroll-snap-align: start;
  background: transparent; /* 移除背景，使用全局背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 0 40px 0;
  overflow: hidden;
  border: none;
}

.team-section-inner {
  max-width: 1600px;
  width: 100%;
  padding: 0 80px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 2;
}

.team-left-content {
  flex: 0 0 35%;
  padding-right: 0;
  margin-top: 0;
  position: relative;
  z-index: 10;
  transform: translateY(-40px);
}

.team-carousel {
  flex: 0 0 65%;
  position: relative;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: -15%;
  z-index: 5;
  padding-bottom: 60px; /* Add space for navigation buttons */
}

.team-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  gap: 0;
  align-items: flex-start;
}

.team-info-area {
  position: relative;
  left: auto;
  top: auto;
  transform: none;
  width: 35%;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
  padding-top: 60px;
}

.team-photos-area {
  position: relative;
  width: 65%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.photos-container {
  width: 100%;
  height: 550px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 1200px; /* 增加透视效果 */
  overflow: visible;
  /* 添加轻微的背景渐变来增强衔接感 */
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.01) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: 20px;
}

/* 优化过渡效果样式 */
.photos-container.transitioning .team-photo-card {
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1), 
              opacity 0.6s cubic-bezier(0.23, 1, 0.32, 1), 
              filter 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 优化向左过渡效果 */
.photos-container.transitioning.right .team-photo-card.active {
  transform: translateX(calc(-100% - 40px)) scale(0.8) rotateY(-5deg);
  opacity: 0.4;
  filter: brightness(0.4) blur(0.5px);
}

.photos-container.transitioning.right .team-photo-card.next {
  transform: translateX(0) scale(1) rotateY(0deg);
  opacity: 1;
  filter: brightness(1) blur(0);
  z-index: 5;
}

.photos-container.transitioning.right .team-photo-card.far-next {
  transform: translateX(calc(100% + 40px)) scale(0.8) rotateY(5deg);
  opacity: 0.4;
}

.photos-container.transitioning.right .team-photo-card.prev {
  transform: translateX(calc(-200% - 80px)) scale(0.65) rotateY(-8deg);
  opacity: 0.25;
}

.photos-container.transitioning.right .team-photo-card.far-prev {
  transform: translateX(calc(-300% - 120px)) scale(0.5) rotateY(-12deg);
  opacity: 0.1;
}

/* 优化向右过渡效果 */
.photos-container.transitioning.left .team-photo-card.active {
  transform: translateX(calc(100% + 40px)) scale(0.8) rotateY(5deg);
  opacity: 0.4;
  filter: brightness(0.4) blur(0.5px);
}

.photos-container.transitioning.left .team-photo-card.prev {
  transform: translateX(0) scale(1) rotateY(0deg);
  opacity: 1;
  filter: brightness(1) blur(0);
  z-index: 5;
}

.photos-container.transitioning.left .team-photo-card.next {
  transform: translateX(calc(200% + 80px)) scale(0.65) rotateY(8deg);
  opacity: 0.25;
}

.photos-container.transitioning.left .team-photo-card.far-prev {
  transform: translateX(calc(-100% - 40px)) scale(0.8) rotateY(-5deg);
  opacity: 0.4;
}

.photos-container.transitioning.left .team-photo-card.far-next {
  transform: translateX(calc(300% + 120px)) scale(0.5) rotateY(12deg);
  opacity: 0.1;
}

.team-photo-card {
  position: absolute;
  width: 400px;
  height: 550px;
  border-radius: 24px; /* 减少圆角避免边缘突兀 */
  overflow: hidden;
  /* 优化阴影效果 */
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), 
              opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1), 
              filter 0.3s cubic-bezier(0.23, 1, 0.32, 1),
              box-shadow 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center center;
  will-change: transform, opacity, filter;
  backface-visibility: hidden;
  /* 添加轻微的边框增强衔接感 */
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.team-photo-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  /* 添加图片的微妙过渡效果 - 与容器过渡时间同步 */
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 优化中间活跃照片样式 */
.team-photo-card.active {
  z-index: 5;
  opacity: 1;
  transform: translateX(0) scale(1) rotateY(0deg);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 12px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transition-duration: 0.4s;
}

/* 优化右侧第一张照片 */
.team-photo-card.next {
  z-index: 4;
  opacity: 0.5; /* 提高透明度让衔接更自然 */
  transform: translateX(calc(100% + 40px)) scale(0.85) rotateY(3deg); /* 减少间距和旋转角度 */
  filter: brightness(0.5) blur(0.3px); /* 减少模糊 */
  transition-duration: 0.4s;
}

/* 优化右侧第二张照片 */
.team-photo-card.far-next {
  z-index: 3;
  opacity: 0.3; /* 提高透明度 */
  transform: translateX(calc(180% + 70px)) scale(0.7) rotateY(6deg); /* 调整位置和比例 */
  filter: brightness(0.3) blur(0.8px); /* 减少模糊 */
  transition-duration: 0.4s;
}

/* 优化左侧第一张照片 */
.team-photo-card.prev {
  z-index: 4;
  opacity: 0.5;
  transform: translateX(calc(-100% - 40px)) scale(0.85) rotateY(-3deg);
  filter: brightness(0.5) blur(0.3px);
  transition-duration: 0.4s;
}

/* 优化左侧第二张照片 */
.team-photo-card.far-prev {
  z-index: 3;
  opacity: 0.3;
  transform: translateX(calc(-180% - 70px)) scale(0.7) rotateY(-6deg);
  filter: brightness(0.3) blur(0.8px);
  transition-duration: 0.4s;
}

/* 添加悬停效果增强交互性 - 修复抖动问题 */
/* 为每个位置状态定义专门的悬停效果，避免样式冲突 */

.team-photo-card.active:hover {
  transform: translateX(0) scale(1.02) rotateY(0deg) translateY(-5px);
  box-shadow: 
    0 30px 60px rgba(0, 0, 0, 0.3),
    0 15px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.team-photo-card.next:hover {
  transform: translateX(calc(100% + 40px)) scale(0.85) rotateY(3deg) translateY(-5px);
}

.team-photo-card.prev:hover {
  transform: translateX(calc(-100% - 40px)) scale(0.85) rotateY(-3deg) translateY(-5px);
}

.team-photo-card.far-next:hover {
  transform: translateX(calc(180% + 70px)) scale(0.7) rotateY(6deg) translateY(-5px);
}

.team-photo-card.far-prev:hover {
  transform: translateX(calc(-180% - 70px)) scale(0.7) rotateY(-6deg) translateY(-5px);
}

.team-photo-card.active:hover img {
  transform: scale(1.05);
}

.team-navigation {
  position: absolute;
  display: flex;
  gap: 16px;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  justify-content: center;
  width: 100%;
  padding-top: 20px;
}

.member-info-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 0;
  animation: fadeIn 0.6s ease forwards;
  will-change: opacity, transform;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px 30px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.5) 70%, transparent);
  border-radius: 0 0 24px 24px; /* 修复：与容器的border-radius保持一致 */
}

.member-info-content .member-role,
.member-info-content .member-name,
.member-info-content .member-social {
  opacity: 0;
  animation: slideUp 0.5s ease forwards;
}

.member-info-content .member-role {
  font-family: 'Rajdhani', sans-serif;
  font-size: 16px;
  color: #8c7bff; /* 紫色调 */
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  animation-delay: 0.3s;
}

.member-info-content .member-name {
  font-family: 'Rajdhani', sans-serif;
  font-size: 42px; /* 更适合的字体大小 */
  color: #ffffff;
  margin: 0 0 15px 0;
  font-weight: 600;
  line-height: 1;
  animation-delay: 0.4s;
}

.member-info-content .member-social {
  display: flex;
  gap: 16px;
  margin-top: 10px;
  animation-delay: 0.5s;
}

.social-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: rgba(140, 123, 255, 0.3);
  transform: translateY(-3px);
}

.social-icon svg {
  width: 18px;
  height: 18px;
  transition: all 0.2s ease;
}

.social-icon:hover svg {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 媒体查询调整 */
@media (max-width: 1600px) {
  .team-section-inner {
    padding: 0 60px;
  }

  .team-layout {
    gap: 50px;
  }

  .team-photo-card {
    width: 350px;
    height: 480px;
  }

  .member-info-content .member-name {
    font-size: 36px;
  }
}

@media (max-width: 1200px) {
  .team-section-inner {
    padding: 0 40px;
  }

  .team-left-content {
    flex: 0 0 30%;
  }

  .team-carousel {
    flex: 0 0 70%;
  }

  .team-photo-card {
    width: 320px;
    height: 440px;
  }

  .member-info-content .member-name {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .team-section-inner {
    padding: 0 30px;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .team-left-content {
    flex: 0 0 auto;
    padding-right: 0;
    text-align: center;
    width: 100%;
    margin-top: 20px;
  }

  .team-carousel {
    flex: 0 0 auto;
    width: 100%;
    padding-top: 20px;
  }

  .team-layout {
    flex-direction: column-reverse;
    gap: 40px;
  }

  .team-info-area {
    width: 100%;
    margin-top: 40px;
    text-align: center;
    padding-right: 0;
    padding-top: 0;
  }

  .team-photos-area {
    width: 100%;
  }

  .photos-container {
    height: 440px;
    /* 优化移动端背景 */
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 255, 255, 0.015) 50%,
      rgba(255, 255, 255, 0.03) 100%);
  }

  .team-photo-card {
    width: 280px;
    height: 380px;
  }

  /* 移动端优化照片位置，减少间距和旋转角度 */
  .team-photo-card.next {
    transform: translateX(calc(100% + 25px)) scale(0.88) rotateY(2deg);
  }

  .team-photo-card.prev {
    transform: translateX(calc(-100% - 25px)) scale(0.88) rotateY(-2deg);
  }

  .team-photo-card.far-next {
    transform: translateX(calc(160% + 45px)) scale(0.75) rotateY(4deg);
  }

  .team-photo-card.far-prev {
    transform: translateX(calc(-160% - 45px)) scale(0.75) rotateY(-4deg);
  }

  .team-navigation {
    justify-content: center;
  }

  .member-info-content {
    position: absolute;
    text-align: center;
    padding: 20px;
  }

  .member-info-content .member-social {
    justify-content: center;
  }

  .member-info-content .member-name {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .member-info-content .member-role {
    margin-bottom: 5px;
    font-size: 14px;
  }

  .social-icon {
    width: 28px;
    height: 28px;
  }

  .social-icon svg {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 576px) {
  .team-section-inner {
    padding: 0 20px;
  }

  .photos-container {
    height: 380px;
    /* 小屏幕进一步优化背景 */
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.04) 0%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.04) 100%);
    border-radius: 16px;
  }

  .team-photo-card {
    width: 230px;
    height: 320px;
    border-radius: 20px; /* 减小圆角 */
  }

  /* 小屏幕优化照片间距和效果 */
  .team-photo-card.next {
    transform: translateX(calc(100% + 15px)) scale(0.82) rotateY(1.5deg);
    opacity: 0.6; /* 提高透明度 */
    filter: brightness(0.6) blur(0.2px);
  }

  .team-photo-card.prev {
    transform: translateX(calc(-100% - 15px)) scale(0.82) rotateY(-1.5deg);
    opacity: 0.6;
    filter: brightness(0.6) blur(0.2px);
  }

  .team-photo-card.far-next {
    opacity: 0.35; /* 提高透明度 */
    transform: translateX(calc(140% + 30px)) scale(0.65) rotateY(3deg);
    filter: brightness(0.35) blur(0.5px);
  }

  .team-photo-card.far-prev {
    opacity: 0.35;
    transform: translateX(calc(-140% - 30px)) scale(0.65) rotateY(-3deg);
    filter: brightness(0.35) blur(0.5px);
  }

  /* 优化过渡时的小屏幕效果 */
  .photos-container.transitioning.right .team-photo-card.active {
    transform: translateX(calc(-100% - 15px)) scale(0.82) rotateY(-2deg);
  }

  .photos-container.transitioning.right .team-photo-card.far-next {
    transform: translateX(calc(100% + 15px)) scale(0.82) rotateY(2deg);
  }

  .photos-container.transitioning.left .team-photo-card.active {
    transform: translateX(calc(100% + 15px)) scale(0.82) rotateY(2deg);
  }

  .photos-container.transitioning.left .team-photo-card.far-prev {
    transform: translateX(calc(-100% - 15px)) scale(0.82) rotateY(-2deg);
  }

  .member-info-content .member-name {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .member-info-content .member-role {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .social-icon {
    width: 28px;
    height: 28px;
  }

  .social-icon svg {
    width: 16px;
    height: 16px;
  }
}

@media screen and (max-width: 1200px) {
  .content-section {
    padding: 100px 20px 0 20px; /* 调整中等屏幕的顶部padding */
  }

  .earth-container {
    right: -400px;
  }

  .main-title {
    font-size: 4.5rem;
  }
}

@media screen and (max-width: 992px) {
  .welcome-page {
    flex-direction: column;
    height: auto;
  }

  .content-section {
    width: 100%;
    padding: 120px 20px 80px 20px; /* 增加顶部padding，保持底部padding */
    margin: 0;
    justify-content: flex-start; /* 改为flex-start */
    text-align: center;
    min-height: 100vh;
    align-items: flex-start; /* 确保内容从顶部开始 */
  }

  .team-section-inner {
    flex-direction: column;
    gap: 30px;
  }

  .team-left-content {
    padding-right: 0;
    text-align: center;
  }

  .team-heading {
    text-align: center;
    max-width: 100%;
  }

  .team-right-content {
    width: 100%;
  }

  .team-member-photo {
    max-width: 300px;
    height: 400px;
  }

  .earth-container {
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    position: fixed;
  }

  .company-name {
    font-size: 4rem;
  }

  .company-desc {
    font-size: 1rem;
    padding: 0 20px;
  }

  .team-member-content {
    flex-direction: column-reverse;
    gap: 20px;
  }

  .member-info {
    width: 100%;
    padding: 20px;
    text-align: center;
  }

  .member-name {
    font-size: 36px;
  }

  .member-social {
    justify-content: center;
  }

  .member-photo-container {
    width: 100%;
    height: 350px;
  }

  .team-heading {
    text-align: center;
  }

  .team-title {
    font-size: 42px;
  }

  .team-subtitle {
    font-size: 20px;
  }
}

@media screen and (max-width: 576px) {
  .title-section {
    max-width: 100%;
    margin-top: 30px; /* 在小屏幕上减少顶部边距 */
  }

  .button-section {
    flex-direction: column;
  }

  .company-name {
    font-size: 3rem;
    margin-bottom: 20px;
  }

  .company-desc {
    font-size: 0.9rem;
    margin-bottom: 25px;
    padding: 0;
  }

  .scroll-down-container {
    margin-left: 0;
    margin-top: 15px;
  }

  .learn-more-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  .member-name {
    font-size: 28px;
  }

  .member-role {
    font-size: 12px;
  }

  .team-title {
    font-size: 32px;
  }

  .team-subtitle {
    font-size: 18px;
  }
}

@media screen and (max-width: 360px) {
  .company-name {
    font-size: 2.5rem;
  }

  .company-desc {
    font-size: 0.85rem;
    margin-bottom: 20px;
  }

  .learn-more-btn {
    padding: 8px 20px;
    font-size: 12px;
  }
}

.company-desc::-webkit-scrollbar {
  width: 5px;
}

.company-desc::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

.company-desc::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
}

.company-desc::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink {
  50% { border-color: transparent }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0); }
}

@keyframes glow {
  0% { box-shadow: 0 0 15px rgba(50, 130, 252, 0.5); }
  50% { box-shadow: 0 0 30px rgba(50, 130, 252, 0.8); }
  100% { box-shadow: 0 0 15px rgba(50, 130, 252, 0.5); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.team-member-slide.active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.animate-text {
  opacity: 0;
  animation: fadeInLeft 0.8s ease forwards;
}

.animate-fade-in {
  opacity: 0;
  animation: fadeIn 1s ease 0.5s forwards;
}

.team-member-slide.active .member-role {
  animation-delay: 0.2s;
}

.team-member-slide.active .member-name {
  animation-delay: 0.4s;
}

.highlight-text {
  position: relative;
  display: inline-block;
  color: #4d95ff;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 0 3px;
}

.highlight-text:hover {
  color: #77b0ff;
}

.highlight-text.wave {
  color: #64ffda;
}

.highlight-text.wave::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, rgba(100, 255, 218, 0), rgba(100, 255, 218, 1), rgba(100, 255, 218, 0));
  animation: waveMove 2s infinite;
  transform-origin: bottom center;
  opacity: 0.7;
}

.highlight-text.circle {
  color: #ff7eb3;
}

.highlight-text.circle::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff7eb3;
  box-shadow: 0 0 10px rgba(255, 126, 179, 0.7);
  opacity: 0.6;
  transition: all 0.3s ease;
}

@keyframes waveMove {
  0% {
    transform: scaleX(1);
    opacity: 0.7;
  }
  50% {
    transform: scaleX(1.1);
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 0.7;
  }
}

.highlight-text.circle:hover::before {
  transform: translateX(-50%) scale(1.5);
  opacity: 0.8;
  box-shadow: 0 0 15px rgba(255, 126, 179, 0.9);
}

/* Interactive analytics - Blue */
.highlight-text.diamond {
  color: #4dabf7;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.diamond::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #4dabf7;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.diamond:hover {
  color: #339af0;
  text-shadow: 0 0 10px rgba(77, 171, 247, 0.3);
}

.highlight-text.diamond:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Customizable deployment - Purple */
.highlight-text.underline {
  color: #9c36b5;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.underline::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #9c36b5;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.underline:hover {
  color: #862e9c;
  text-shadow: 0 0 10px rgba(156, 54, 181, 0.3);
}

.highlight-text.underline:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* End-to-end decision support - Teal */
.highlight-text.highlight {
  color: #20c997;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #20c997;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.highlight:hover {
  color: #12b886;
  text-shadow: 0 0 10px rgba(32, 201, 151, 0.3);
}

.highlight-text.highlight:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

@keyframes dataGlow {
  0% {
    text-shadow: 0 0 8px rgba(106, 90, 205, 0.4);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 20px rgba(147, 112, 219, 0.6),
                 0 0 30px rgba(186, 162, 224, 0.3);
    filter: brightness(1.1);
  }
}

@keyframes dataFlow {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes floatUp {
  0% {
    transform: translateY(0) scale(0.5);
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-20px) scale(1);
    opacity: 0;
  }
}

/* Keyframes for animations */
@keyframes holographic {
  0% {
    text-shadow: 0 0 8px rgba(0, 243, 255, 0.7);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 243, 255, 0.9), 0 0 30px rgba(0, 255, 200, 0.6);
    filter: brightness(1.2);
  }
}

@keyframes digitalPulse {
  0%, 100% {
    opacity: 0.8;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
  }
  50% {
    opacity: 1;
    text-shadow: 0 0 20px rgba(255, 0, 255, 0.8), 0 0 30px rgba(255, 0, 255, 0.5);
  }
}

@keyframes circuitPulse {
  0%, 100% {
    border-image-slice: 1;
    border-image-source: linear-gradient(90deg, #00ff88, #00ffea);
  }
  50% {
    border-image-source: linear-gradient(90deg, #00ffea, #00ff88);
  }
}

@keyframes circuitDot {
  0%, 100% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

.highlight-text.wave:hover::after {
  animation-duration: 1s;
  opacity: 1;
}

/* 数据可视化部分 */
.data-section {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 20;
  scroll-snap-align: start;
  background: rgba(0, 0, 0, 0.92);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  overflow: hidden;
}

.data-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.08'/%3E%3C/svg%3E");
  opacity: 0.25;
  pointer-events: none;
  display: block;
}

.data-section-inner {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 40px;
  position: relative;
  z-index: 2;
  background: radial-gradient(circle at 25% 50%, rgba(1, 3, 19, 0.3), transparent 60%);
}

.data-visualization-container {
  position: relative;
  width: 50%;
  height: 600px;
  z-index: 1;
  filter: drop-shadow(0 0 30px rgba(100, 160, 255, 0.3));
  overflow: visible;
}

.data-visualization-container canvas {
  border-radius: 50%;
  box-shadow: inset 0 0 50px rgba(100, 160, 255, 0.3);
}

/* 修改内容区域，增加与地球的视觉连接 */
.data-content {
  position: relative;
  z-index: 10;
  max-width: 50%;
  padding: 50px 60px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: linear-gradient(135deg, rgba(1, 3, 19, 0.7) 0%, rgba(1, 3, 19, 0.5) 100%);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 40px 0 rgba(88, 101, 242, 0.2);
  border: 1px solid rgba(100, 107, 255, 0.08);
  margin-left: -50px; /* 向左移动，与地球部分重叠 */
}

/* 添加闪光边框效果 */
.data-content::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(88, 101, 242, 0.2) 25%,
    rgba(123, 90, 255, 0.4) 50%,
    rgba(88, 101, 242, 0.2) 75%,
    transparent 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  animation: borderRotate 6s linear infinite;
}

@keyframes borderRotate {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

/* 添加背景微粒子 */
.data-content::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(123, 90, 255, 0.02) 0%, transparent 8%),
    radial-gradient(circle at 80% 40%, rgba(88, 101, 242, 0.02) 0%, transparent 8%),
    radial-gradient(circle at 40% 70%, rgba(147, 112, 219, 0.02) 0%, transparent 8%),
    radial-gradient(circle at 70% 90%, rgba(100, 160, 255, 0.02) 0%, transparent 8%);
  opacity: 0.3; /* 大幅降低不透明度 */
  z-index: -1;
}

/* 添加闪光边框效果 */
.data-content::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(88, 101, 242, 0.2) 25%,
    rgba(123, 90, 255, 0.4) 50%,
    rgba(88, 101, 242, 0.2) 75%,
    transparent 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  animation: borderRotate 6s linear infinite;
}

.data-content::after {
  content: '';
  position: absolute;
  left: -50%;
  top: -100%;
  width: 200%;
  height: 300%;
  background: linear-gradient(120deg,
    rgba(88, 101, 242, 0.05) 0%,
    rgba(101, 134, 255, 0.07) 25%,
    rgba(114, 137, 218, 0.08) 50%,
    rgba(126, 87, 194, 0.07) 75%,
    rgba(80, 72, 170, 0.05) 100%);
  filter: blur(40px);
  opacity: 0.6;
  z-index: 2;
  animation: flowLight 12s linear infinite;
  transform-origin: center center;
}

@keyframes flowLight {
  0% { transform: rotate(0deg) scale(1); opacity: 0.5;}
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.7;}
  100% { transform: rotate(360deg) scale(1); opacity: 0.5;}
}

.data-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 3.4rem;
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.2;
  text-transform: uppercase;
  margin-bottom: 18px;
  position: relative;
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.2));
}

.title-word {
  position: relative;
  display: inline-block;
  background: linear-gradient(90deg, #e0f7ff 0%, #c4a5ff 50%, #a2a8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.1em;
  transform-origin: left;
  animation: wordAppear 0.8s cubic-bezier(0.215, 0.61, 0.355, 1) backwards;
}

.title-word:nth-child(1) { animation-delay: 0.1s; }
.title-word:nth-child(2) { animation-delay: 0.2s; }
.title-word:nth-child(3) { animation-delay: 0.3s; }
.title-word:nth-child(4) { animation-delay: 0.4s; }
.title-word:nth-child(5) { animation-delay: 0.5s; }
.title-word:nth-child(6) { animation-delay: 0.6s; }

.title-word::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6246ea 0%, #a594f9 100%);
  animation: lineGrow 0.6s ease-out forwards;
  animation-delay: inherit;
  opacity: 0.7;
  filter: blur(0.5px);
}

@keyframes wordAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes lineGrow {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 0.7;
  }
}

.data-content .enhanced-divider {
  width: 120px;
  height: 1px;
  margin: 8px 0 25px 0;
  background: linear-gradient(90deg, rgba(182, 234, 255, 0.01) 0%, rgba(147, 112, 219, 0.8) 50%, rgba(182, 234, 255, 0.01) 100%);
  position: relative;
  z-index: 3;
  overflow: hidden;
}

.data-content .enhanced-divider::after {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 20%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0.9;
  filter: blur(1px);
  animation: dividerFlow 2s linear infinite;
}

@keyframes dividerFlow {
  0% { left: -20%; width: 20%;}
  100% { left: 100%; width: 20%;}
}

.data-subtitle-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 10px;
  z-index: 3;
  position: relative;
  width: 100%;
}

.data-subtitle.main-subtitle {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.25rem;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.6;
  position: relative;
  padding-left: 0;
  margin-bottom: 0;
  animation: subtitleFadeIn 1.2s cubic-bezier(.4,0,.2,1) both;
  text-shadow: 0 0 30px rgba(123, 90, 255, 0.6);
}

.data-subtitle.main-subtitle::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, rgba(123, 90, 255, 0.1), rgba(123, 90, 255, 0.8), rgba(123, 90, 255, 0.1));
  border-radius: 1px;
}

.data-subtitle.secondary-subtitle {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(220, 230, 255, 0.75);
  font-size: 1rem;
  font-weight: 300;
  letter-spacing: 0.015em;
  line-height: 1.6;
  position: relative;
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
  animation: subtitleFadeIn 1.6s cubic-bezier(.4,0,.2,1) 0.2s both;
}

.data-subtitle.secondary-subtitle::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom, rgba(182, 234, 255, 0.1), rgba(182, 234, 255, 0.4), rgba(182, 234, 255, 0.1));
  border-radius: 1px;
}

@keyframes subtitleFadeIn {
  0% { opacity: 0; transform: translateY(12px);}
  100% { opacity: 1; transform: translateY(0);}
}

/* 添加微动效 */
.data-content:hover .enhanced-divider::after {
  animation-duration: 1.5s;
}

.data-content:hover .data-title {
  animation-duration: 3s;
}

.request-access-btn {
  margin-top: 20px;
  background: linear-gradient(to right, #7b5aff, #6246ea);
  color: #ffffff;
  border: none;
  padding: 16px 36px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(123, 90, 255, 0.4), 0 0 30px rgba(123, 90, 255, 0.3);
  text-transform: uppercase;
  outline: none;
  position: relative;
  overflow: hidden;
  z-index: 1;
  align-self: flex-start;
}

.request-access-btn::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(90deg, #7b5aff, #6246ea, #7b5aff);
  z-index: -1;
  border-radius: 50px;
  background-size: 200%;
  animation: glowingBorder 3s linear infinite;
}

.request-access-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(123, 90, 255, 0.5);
  background: linear-gradient(to right, #8b6aff, #7256fb);
}

.request-access-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(123, 90, 255, 0.3);
}

@keyframes glowingBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes floatElement {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0px); }
}

/* 媒体查询适配 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    flex-direction: column;
  }

  .data-visualization-container {
    width: 100%;
    height: 50vh;
    margin-bottom: 20px;
    min-height: 400px;
  }

  .data-content {
    max-width: 100%;
    text-align: center;
    align-items: center;
    padding: 30px;
  }

  .data-title {
    font-size: 3.2rem;
    text-align: center;
  }

  .data-subtitle {
    font-size: 1.6rem;
    margin-bottom: 40px;
    text-align: center;
    max-width: 100%;
  }

  .data-content .divider-line {
    margin: 15px auto 25px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.9), transparent);
  }

  .request-access-btn {
    align-self: center;
  }
}

@media screen and (max-width: 576px) {
  .data-title {
    font-size: 2.4rem;
  }

  .data-subtitle {
    font-size: 1.6rem;
    margin-bottom: 45px;
  }

  .data-content {
    padding: 20px;
  }

  .request-access-btn {
    padding: 14px 28px;
    font-size: 1rem;
  }
}

/* 申请访问模态框 */
.request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(1, 3, 19, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-in-out;
}

.request-modal {
  background: linear-gradient(135deg, rgba(1, 3, 19, 0.95) 0%, rgba(1, 3, 19, 0.95) 100%);
  border-radius: 20px;
  padding: 40px;
  width: 90%;
  max-width: 550px;
  position: relative;
  box-shadow: 0 0 50px rgba(100, 160, 255, 0.2);
  border: 1px solid rgba(100, 160, 255, 0.15);
  animation: scaleIn 0.3s ease-in-out;
  overflow: hidden;
}

.request-modal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, #7b5aff, #6246ea);
  z-index: 1;
}

.close-modal-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: none;
  color: #a8aabc;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-modal-btn:hover {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-title {
  color: #ffffff;
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 600;
  letter-spacing: 1px;
  text-align: center;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.modal-description {
  color: #a8aabc;
  text-align: center;
  margin-bottom: 30px;
  font-size: 1rem;
  line-height: 1.6;
}

.request-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.form-group {
  width: 100%;
}

.form-input {
  width: 100%;
  padding: 14px;
  border-radius: 10px;
  border: 1px solid rgba(100, 160, 255, 0.3);
  background-color: rgba(1, 3, 19, 0.5);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.form-input::placeholder {
  color: #8a8daa;
}

.form-input:focus {
  border-color: rgba(123, 90, 255, 0.6);
  box-shadow: 0 0 0 2px rgba(123, 90, 255, 0.2);
}

.form-input:hover {
  border-color: rgba(123, 90, 255, 0.5);
}

.submit-request-btn {
  background: linear-gradient(to right, #7b5aff, #6246ea);
  color: #ffffff;
  border: none;
  padding: 16px 28px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(123, 90, 255, 0.4);
  text-transform: uppercase;
  outline: none;
  margin-top: 10px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.submit-request-btn.submitting {
  background: linear-gradient(to right, #6b4aef, #5236da);
  cursor: not-allowed;
  opacity: 0.8;
}

.submit-request-btn.submitting::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loadingEffect 1.5s infinite;
}

.submit-request-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(123, 90, 255, 0.5);
  background: linear-gradient(to right, #8b6aff, #7256fb);
}

.submit-request-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(123, 90, 255, 0.3);
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 媒体查询适配 */
@media screen and (max-width: 576px) {
  .request-modal {
    padding: 30px 20px;
  }

  .modal-title {
    font-size: 1.6rem;
  }

  .modal-description {
    font-size: 0.9rem;
    margin-bottom: 20px;
  }

  .form-input {
    padding: 12px;
  }

  .submit-request-btn {
    padding: 14px 20px;
  }
}

/* Starry Button Styles */
.starry-button {
  position: relative;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 30px;
  font-size: 1rem;
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 2px;
  text-transform: uppercase;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 50px;
  backdrop-filter: blur(5px);
  z-index: 1;
}

.starry-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(100, 200, 255, 0.1), rgba(0, 100, 255, 0.1));
  z-index: -1;
  transition: all 0.3s ease;
  opacity: 0.5;
}

.starry-button:hover {
  border-color: rgba(100, 200, 255, 0.8);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  transform: translateY(-2px);
}

.starry-button:active {
  transform: translateY(0);
}

.starry-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(100, 200, 255, 0.3);
}

.starry-button-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.starry-button-text {
  position: relative;
  z-index: 1;
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
  transition: all 0.3s ease;
}

.starry-button:hover .starry-button-text {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
}

/* Button glow effect */
@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(100, 200, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(100, 200, 255, 0.8);
  }
}

.starry-button.animate-glow {
  animation: buttonGlow 2s infinite;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .starry-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

.form-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(to right, #64ffda, #34eea6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #ffffff;
  margin-bottom: 20px;
  box-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
  animation: successPulse 2s infinite;
}

@keyframes loadingEffect {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes successPulse {
  0% {
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px rgba(100, 255, 218, 0.7);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
    transform: scale(1);
  }
}

/* 导航按钮样式 */
.nav-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(140, 123, 255, 0.2);
  border: 1px solid rgba(140, 123, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(149, 128, 255, 0.7) 0%, rgba(140, 123, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 50%;
}

.nav-button:hover {
  background: rgba(140, 123, 255, 0.4);
  border-color: rgba(180, 163, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(140, 123, 255, 0.5);
}

.nav-button:hover::before {
  opacity: 0.8;
  transform: scale(1.5);
  animation: pulseGlow 1.5s infinite alternate;
}

.nav-button:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
  box-shadow: 0 0 8px rgba(140, 123, 255, 0.3);
}

.nav-button svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.nav-button:hover svg {
  transform: scale(1.1);
  stroke: #ffffff;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  animation: pulseIcon 1s infinite alternate;
}

@keyframes pulseGlow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes pulseIcon {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  }
  100% {
    transform: scale(1.15);
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
  }
}

.team-heading {
  text-align: left;
  padding-top: 60px;
}

.team-title {
  font-size: 80px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 2px;
  line-height: 1.1;
  font-family: 'Rajdhani', sans-serif;
  background: linear-gradient(to right, #ffffff, #a8aabc);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.team-title::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #8c7bff, transparent);
}

.team-subtitle {
  font-size: 28px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  letter-spacing: 1px;
  font-family: 'Rajdhani', sans-serif;
  position: relative;
}

/* 流光效果 */
.glow-effect {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.2;
  z-index: 1;
  pointer-events: none;
  transition: all 0.8s ease;
}

.glow-effect.top-left {
  top: -80px;
  left: -80px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(126, 87, 194, 0.4) 0%, rgba(126, 87, 194, 0.1) 70%, transparent 100%);
  animation: pulseGlow 8s infinite alternate;
}

.glow-effect.bottom-right {
  bottom: -100px;
  right: -100px;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(88, 101, 242, 0.4) 0%, rgba(101, 134, 255, 0.1) 70%, transparent 100%);
  animation: pulseGlow 8s infinite alternate-reverse;
}

@keyframes pulseGlow {
  0% { transform: scale(1); opacity: 0.2; }
  50% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(1); opacity: 0.2; }
}

/* 微噪点层 */
.noise-layer {
  position: absolute;
  inset: 0;
  z-index: 2;
  pointer-events: none;
  opacity: 0.04;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.95' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.4'/%3E%3C/svg%3E");
  mix-blend-mode: overlay;
}

/* 极细动态边框 */
.dynamic-border {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 3;
  border: 1px solid rgba(147, 112, 219, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.dynamic-border::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: borderFlow 4s linear infinite;
}

@keyframes borderFlow {
  0% { left: -50%; top: 0; }
  25% { left: 100%; top: 0; }
  25.1% { left: 100%; top: 0; }
  25.2% { left: 100%; top: 0; }
  50% { left: 100%; top: 100%; }
  50.1% { top: 100%; left: 100%; }
  50.2% { top: 100%; left: 100%; }
  75% { left: -50%; top: 100%; }
  75.1% { left: -50%; top: 100%; }
  75.2% { left: -50%; top: 100%; }
  100% { left: -50%; top: 0; }
}

.data-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 3.4rem;
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.2;
  text-transform: uppercase;
  margin-bottom: 18px;
  position: relative;
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.2));
}

.text-flow {
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
}

.text-highlight {
  background: linear-gradient(90deg, #e0f7ff 0%, #c4a5ff 50%, #a2a8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.3));
  animation: textPulse 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

@keyframes textPulse {
  0%, 100% { filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.3)); transform: translateY(0) scale(1);}
  50% { filter: drop-shadow(0 0 20px rgba(160, 141, 255, 0.5)); transform: translateY(-2px) scale(1.01);}
}

.data-content .enhanced-divider {
  width: 120px;
  height: 1px;
  margin: 8px 0 25px 0;
  background: linear-gradient(90deg, rgba(182, 234, 255, 0.01) 0%, rgba(147, 112, 219, 0.6) 50%, rgba(182, 234, 255, 0.01) 100%);
  position: relative;
  z-index: 3;
  overflow: hidden;
}

.divider-glow {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 20%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.01) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.01) 100%);
  opacity: 0.9;
  filter: blur(1px);
  animation: dividerFlow 2s linear infinite;
}

/* 媒体查询 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    flex-direction: column;
    padding: 30px 20px;
  }

  .data-visualization-container {
    width: 100%;
    height: 400px;
    margin-bottom: 30px;
  }

  .data-content {
    max-width: 100%;
    width: 100%;
    padding: 40px 30px;
    align-items: center;
    text-align: center;
  }

  .data-title {
    font-size: 2.5rem;
    text-align: center;
    align-items: center;
  }

  .data-content .enhanced-divider {
    margin-left: auto;
    margin-right: auto;
  }

  .data-subtitle-group {
    align-items: center;
  }

  .data-subtitle.main-subtitle,
  .data-subtitle.secondary-subtitle {
    text-align: center;
  }

  .data-subtitle.main-subtitle::before,
  .data-subtitle.secondary-subtitle::before {
    display: none;
  }
}

@media screen and (max-width: 576px) {
  .data-content {
    padding: 30px 20px;
  }

  .data-title {
    font-size: 1.8rem;
  }

  .data-subtitle.main-subtitle {
    font-size: 1rem;
  }

  .data-subtitle.secondary-subtitle {
    font-size: 0.9rem;
  }
}

/* 悬停交互效果 */
.data-content:hover .dynamic-border::after {
  animation-duration: 2.5s;
}

.data-content:hover .glow-effect.top-left {
  opacity: 0.3;
  transform: scale(1.1);
}

.data-content:hover .glow-effect.bottom-right {
  opacity: 0.3;
  transform: scale(1.1);
}

.data-content:hover .text-highlight {
  filter: drop-shadow(0 0 20px rgba(160, 141, 255, 0.5));
}

/* 添加连接线和粒子效果以使地球和文本更加融合 */
.globe-connector {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
  overflow: hidden;
}

.connector-line {
  position: absolute;
  background: linear-gradient(90deg, rgba(100, 160, 255, 0.1) 0%, rgba(147, 112, 219, 0.3) 50%, rgba(100, 160, 255, 0.1) 100%);
  height: 1px;
  transform-origin: left center;
  opacity: 0;
  filter: blur(1px);
  animation: lineAppear 2s ease-out forwards, lineGlow 4s infinite alternate;
}

@keyframes lineAppear {
  0% { width: 0; opacity: 0; }
  100% { width: 100%; opacity: 1; }
}

@keyframes lineGlow {
  0% { opacity: 0.2; }
  100% { opacity: 0.6; }
}

/* 添加发光边缘效果连接地球和内容 */
.globe-overlay-gradient {
  position: absolute;
  top: 0;
  right: -120px;
  width: 200px;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(16, 20, 36, 0) 0%,
    rgba(88, 101, 242, 0.05) 20%,
    rgba(88, 101, 242, 0.1) 40%,
    rgba(88, 101, 242, 0.2) 60%,
    rgba(88, 101, 242, 0.3) 80%,
    rgba(16, 20, 36, 0.5) 100%
  );
  filter: blur(20px);
  z-index: 4;
  pointer-events: none;
  opacity: 0.8;
}

/* 给地球容器添加轻微的光晕效果 */
.data-visualization-container::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 150px;
  height: 150px;
  transform: translateY(-50%);
  background: radial-gradient(
    circle at right,
    rgba(100, 160, 255, 0.15),
    rgba(100, 160, 255, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(15px);
  z-index: 3;
}

/* 添加粒子流效果连接地球和内容 */
.particle-stream {
  position: absolute;
  left: 30%;
  top: 0;
  bottom: 0;
  width: 100px;
  z-index: 3;
  pointer-events: none;
  overflow: hidden;
}

.particle-dot {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background: rgba(77, 200, 255, 0.6); /* 改为青色，与主题一致 */
  filter: blur(1px);
  opacity: 0;
  animation: particleFlow 3s linear infinite;
}

@keyframes particleFlow {
  0% {
    transform: translate(0, -100%) scale(0.3);
    opacity: 0;
  }
  30% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translate(100px, 100%) scale(1.2);
    opacity: 0;
  }
}

@keyframes particleFlowVertical {
  0% {
    transform: translate(-100%, 0) scale(0.3);
    opacity: 0;
  }
  30% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translate(100%, 30px) scale(1.2);
    opacity: 0;
  }
}

/* 移动端媒体查询调整 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    background: radial-gradient(circle at 50% 30%, rgba(1, 3, 19, 0.3), transparent 70%);
  }

  .data-content {
    margin-left: 0;
    margin-top: -50px; /* 向上移动，与地球部分重叠 */
  }

  .globe-overlay-gradient {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 150px;
    background: linear-gradient(0deg,
      rgba(16, 20, 36, 0.5) 0%,
      rgba(88, 101, 242, 0.2) 40%,
      rgba(16, 20, 36, 0) 100%
    );
  }

  .particle-stream {
    left: 0;
    right: 0;
    top: auto;
    bottom: 30%;
    width: 100%;
    height: 100px;
  }

  .particle-dot {
    animation: particleFlowVertical 3s linear infinite;
  }
}

@media (max-width: 768px) {
  .data-cta-button .starry-button,
  .explore-platform-btn {
    padding: 12px 28px;
    font-size: 0.95rem;
  }
}

/* 重构后的工业园区定位模块 - 更优美的设计 */
.futuristic-industrial-module {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  overflow: hidden;
  z-index: 20;
  scroll-snap-align: start;
  padding: 60px 0;
}

/* 动态背景层系统 */
.industrial-bg-layers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* 增强版网格背景 */
.industrial-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 150, 255, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 150, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, rgba(0, 200, 255, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(100, 150, 255, 0.04) 0%, transparent 50%);
  background-size: 60px 60px, 60px 60px, 800px 800px, 600px 600px;
  opacity: 0.8;
  animation: gridPulse 8s ease-in-out infinite;
}

@keyframes gridPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.9; }
}

/* 精细电路图案 */
.circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='rgba(0, 180, 255, 0.12)' stroke-width='1.5' d='M20,40 L40,40 L40,20 M80,20 L80,40 L100,40 M100,80 L80,80 L80,100 M40,100 L40,80 L20,80 M40,60 L80,60 M60,40 L60,80'/%3E%3Ccircle cx='40' cy='40' r='3' fill='rgba(0, 200, 255, 0.3)'/%3E%3Ccircle cx='80' cy='80' r='3' fill='rgba(0, 200, 255, 0.3)'/%3E%3C/svg%3E");
  background-size: 160px 160px;
  opacity: 0.3;
  animation: circuitFlow 12s linear infinite;
}

@keyframes circuitFlow {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-20px) translateY(-10px); }
  50% { transform: translateX(-40px) translateY(-20px); }
  75% { transform: translateX(-20px) translateY(-10px); }
  100% { transform: translateX(0) translateY(0); }
}

/* 能量流线 */
.energy-flow-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 0%, rgba(0, 220, 255, 0.05) 1px, transparent 2px),
    linear-gradient(-45deg, transparent 0%, rgba(100, 180, 255, 0.05) 1px, transparent 2px);
  background-size: 200px 200px, 180px 180px;
  animation: energyFlow 15s linear infinite;
}

@keyframes energyFlow {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 200px 200px, -180px 180px; }
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(0, 220, 255, 0.4), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(100, 180, 255, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(0, 200, 255, 0.5), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(150, 200, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px, 180px 120px, 160px 90px, 140px 110px;
  animation: particlesFloat 20s linear infinite;
}

@keyframes particlesFloat {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-200px) translateY(-100px); }
}

/* 主容器布局 */
.industrial-module-container {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  z-index: 5;
  padding: 0 40px;
  gap: 80px;
  align-items: center;
}

/* 地球展示区域 */
.holographic-earth-section {
  position: relative;
  width: 45%;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 轨道环系统 */
.orbital-ring-system {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.orbit-ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 200, 255, 0.2);
  transform: translate(-50%, -50%);
}

.ring-outer {
  width: 620px;
  height: 620px;
  border: 2px solid rgba(0, 180, 255, 0.15);
  box-shadow: 
    0 0 30px rgba(0, 150, 255, 0.1),
    inset 0 0 30px rgba(0, 200, 255, 0.05);
  animation: ringRotate 60s linear infinite;
}

.ring-middle {
  width: 520px;
  height: 520px;
  border: 1.5px solid rgba(50, 200, 255, 0.25);
  box-shadow: 
    0 0 20px rgba(0, 180, 255, 0.15),
    inset 0 0 20px rgba(50, 220, 255, 0.1);
  animation: ringRotate 45s linear infinite reverse;
}

.ring-inner {
  width: 420px;
  height: 420px;
  border: 1px solid rgba(100, 220, 255, 0.3);
  box-shadow: 
    0 0 15px rgba(50, 200, 255, 0.2),
    inset 0 0 15px rgba(100, 230, 255, 0.1);
  animation: ringRotate 30s linear infinite;
}

@keyframes ringRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 地球显示容器 */
.earth-display-container {
  position: relative;
  width: 500px;
  height: 500px;
  z-index: 10;
}

/* 数据连接线 */
.data-connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8;
  pointer-events: none;
}

.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 220, 255, 0.3) 20%, 
    rgba(100, 200, 255, 0.8) 50%, 
    rgba(0, 220, 255, 0.3) 80%, 
    transparent 100%);
  transform-origin: left center;
  opacity: 0;
  animation: connectionFlow 4s infinite ease-in-out;
}

.line-1 {
  top: 30%;
  right: 10%;
  width: 35%;
  transform: rotate(-15deg);
  animation-delay: 0s;
}

.line-2 {
  top: 50%;
  right: 8%;
  width: 40%;
  transform: rotate(5deg);
  animation-delay: 1.3s;
}

.line-3 {
  top: 70%;
  right: 12%;
  width: 32%;
  transform: rotate(20deg);
  animation-delay: 2.6s;
}

@keyframes connectionFlow {
  0% { 
    opacity: 0;
    transform: scaleX(0);
  }
  20% {
    opacity: 1;
    transform: scaleX(0.3);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
  80% {
    opacity: 0.6;
    transform: scaleX(1);
  }
  100% {
    opacity: 0;
    transform: scaleX(1);
  }
}

/* 全息投影点 */
.hologram-points {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 12;
  pointer-events: none;
}

.holo-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(0, 255, 200, 0.8) 0%, rgba(0, 200, 255, 0.4) 70%, transparent 100%);
  border-radius: 50%;
  box-shadow: 
    0 0 15px rgba(0, 220, 255, 0.6),
    0 0 30px rgba(0, 180, 255, 0.3);
  animation: holoPointPulse 3s infinite ease-in-out;
}

.point-1 {
  top: 25%;
  right: 20%;
  animation-delay: 0s;
}

.point-2 {
  top: 45%;
  right: 15%;
  animation-delay: 0.75s;
}

.point-3 {
  top: 65%;
  right: 25%;
  animation-delay: 1.5s;
}

.point-4 {
  top: 35%;
  right: 35%;
  animation-delay: 2.25s;
}

@keyframes holoPointPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.4);
    opacity: 1;
  }
}

/* 连接线 */
.connection-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 200, 255, 0), rgba(0, 200, 255, 0.8), rgba(0, 200, 255, 0));
  transform-origin: left center;
  z-index: 12;
  opacity: 0;
  animation: lineConnect 3s infinite ease-in-out;
}

.connection-line-1 {
  top: 35%;
  right: 5%;
  width: 20%;
  transform: rotate(-10deg);
  animation-delay: 0.2s;
}

.connection-line-2 {
  top: 55%;
  right: 5%;
  width: 25%;
  transform: rotate(5deg);
  animation-delay: 0.7s;
}

.connection-line-3 {
  top: 40%;
  right: 0;
  width: 20%;
  transform: rotate(-5deg);
  animation-delay: 1.2s;
}

.connection-line-4 {
  top: 60%;
  right: 0;
  width: 22%;
  transform: rotate(10deg);
  animation-delay: 1.7s;
}

@keyframes lineConnect {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}

/* 数据控制中心 */
.command-center-container {
  position: relative;
  width: 55%;
  padding-left: 60px;
  z-index: 15;
}

/* 模块外框 */
.command-center-frame {
  position: relative;
  width: 100%;
  background: rgba(1, 3, 19, 0.7);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
  padding: 40px;
  border: 1px solid rgba(100, 180, 255, 0.2);
}

/* 顶部边框光效 */
.command-center-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(0, 150, 255, 0.1),
    rgba(0, 220, 255, 0.8) 20%,
    rgba(60, 180, 255, 0.8) 50%,
    rgba(0, 220, 255, 0.8) 80%,
    rgba(0, 150, 255, 0.1)
  );
  z-index: 1;
}

/* 角落点缀 */
.corner-accent {
  position: absolute;
  width: 20px;
  height: 20px;
  border-style: solid;
  border-color: rgba(0, 180, 255, 0.6);
  z-index: 2;
}

.top-left-corner {
  top: 0;
  left: 0;
  border-width: 2px 0 0 2px;
  border-radius: 4px 0 0 0;
}

.top-right-corner {
  top: 0;
  right: 0;
  border-width: 2px 2px 0 0;
  border-radius: 0 4px 0 0;
}

.bottom-left-corner {
  bottom: 0;
  left: 0;
  border-width: 0 0 2px 2px;
  border-radius: 0 0 0 4px;
}

.bottom-right-corner {
  bottom: 0;
  right: 0;
  border-width: 0 2px 2px 0;
  border-radius: 0 0 4px 0;
}

/* 内容布局 */
.command-center-content {
  position: relative;
  z-index: 5;
}

/* 标题样式 */
.industrial-title-container {
  margin-bottom: 30px;
}

.industrial-title {
  margin: 0;
  padding: 0;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  line-height: 1.2;
  color: #ffffff;
  margin-bottom: 15px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.industrial-title-line {
  display: block;
  font-size: 2.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  height: 3.4rem;
  opacity: 0;
  transform: translateY(20px);
  animation: titleLineReveal 0.8s forwards cubic-bezier(0.215, 0.61, 0.355, 1);
}

.industrial-title-line:nth-child(1) { animation-delay: 0.1s; }
.industrial-title-line:nth-child(2) { animation-delay: 0.3s; }
.industrial-title-line:nth-child(3) { animation-delay: 0.5s; }

.industrial-title-line::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(0, 150, 255, 0),
    rgba(0, 220, 255, 0.6) 30%,
    rgba(0, 220, 255, 0.6) 70%,
    rgba(0, 150, 255, 0)
  );
  transform: scaleX(0);
  transform-origin: left center;
  animation: titleUnderlineGrow 1.2s forwards ease-out;
  animation-delay: 0.8s;
}

.industrial-title-word {
  display: inline-block;
  position: relative;
}

.industrial-title-highlight {
  color: #00e5ff;
  text-shadow: 0 0 10px rgba(0, 220, 255, 0.6);
  animation: titleHighlightGlow 3s infinite alternate;
}

@keyframes titleLineReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleUnderlineGrow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes titleHighlightGlow {
  0% {
    text-shadow: 0 0 10px rgba(0, 220, 255, 0.6);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 220, 255, 0.9), 0 0 30px rgba(0, 170, 255, 0.6);
  }
}

/* 描述样式 */
.industrial-description {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 25px;
  position: relative;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1s;
}

.industrial-sub-description {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(220, 230, 255, 0.7);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1.2s;
}

@keyframes descriptionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮样式增强 */
.command-center-cta {
  margin-top: 40px;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1.4s;
}

.industrial-cta-button {
  position: relative;
  background: linear-gradient(45deg, rgba(0, 80, 200, 0.8), rgba(0, 150, 255, 0.8));
  color: white;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
  padding: 16px 36px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 100, 255, 0.3);
  z-index: 1;
}

.industrial-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
  z-index: -1;
}

.industrial-cta-button:hover {
  background: linear-gradient(45deg, rgba(0, 100, 220, 0.9), rgba(0, 180, 255, 0.9));
  box-shadow: 0 7px 20px rgba(0, 120, 255, 0.4);
  transform: translateY(-2px);
}

.industrial-cta-button:hover::before {
  left: 100%;
}

.industrial-cta-button:active {
  transform: translateY(1px);
  box-shadow: 0 3px 10px rgba(0, 100, 255, 0.3);
}

/* 数据节点图标 */
.data-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.data-node {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(0, 150, 255, 0.7);
  border-radius: 50%;
  transform: scale(0);
  animation: nodeAppear 3s infinite ease-out;
  box-shadow: 0 0 10px rgba(0, 200, 255, 0.8);
}

.node-1 {
  top: 20%;
  right: 10%;
  animation-delay: 0.5s;
}

.node-2 {
  bottom: 30%;
  right: 15%;
  animation-delay: 1.2s;
}

.node-3 {
  top: 40%;
  left: 10%;
  animation-delay: 1.8s;
}

.node-4 {
  bottom: 20%;
  left: 15%;
  animation-delay: 2.5s;
}

@keyframes nodeAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    transform: scale(1.5);
    opacity: 1;
  }
  40% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .industrial-module-container {
    padding: 0 20px;
  }

  .holographic-earth-container {
    width: 40%;
  }

  .command-center-container {
    width: 60%;
    padding-left: 30px;
  }

  .industrial-title-line {
    font-size: 2.4rem;
    height: 3rem;
  }
}

@media screen and (max-width: 992px) {
  .industrial-module-container {
    flex-direction: column;
    align-items: center;
    padding: 60px 20px;
  }

  .holographic-earth-container {
    width: 100%;
    height: 400px;
    margin-bottom: 40px;
  }

  .command-center-container {
    width: 100%;
    padding-left: 0;
  }

  .command-center-frame {
    padding: 30px;
  }

  .industrial-title-line {
    font-size: 2.2rem;
    height: 2.8rem;
    text-align: center;
  }

  .industrial-description,
  .industrial-sub-description {
    text-align: center;
  }

  .command-center-cta {
    display: flex;
    justify-content: center;
  }

  .earth-connection-point,
  .connection-line {
    display: none;
  }
}

@media screen and (max-width: 576px) {
  .industrial-module-container {
    padding: 40px 15px;
  }

  .holographic-earth-container {
    height: 300px;
  }

  .command-center-frame {
    padding: 20px;
  }

  .industrial-title-line {
    font-size: 1.8rem;
    height: 2.3rem;
  }

  .industrial-description {
    font-size: 1.1rem;
  }

  .industrial-sub-description {
    font-size: 0.9rem;
  }

  .industrial-cta-button {
    padding: 14px 28px;
    font-size: 1rem;
  }
}

/* 新增：现代科技感地球数据模块样式 */
.earth-data-section {
  position: relative;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #010313 0%, #010313 50%, #010313 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.global-map-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
}

.global-map-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
}

@media (max-width: 768px) {
  .global-map-container {
    height: 100vh;
    width: 100vw;
  }
}

/* 简洁页脚样式 */
.site-footer {
  background: linear-gradient(135deg, #010313 0%, #010313 100%);
  border-top: 1px solid rgba(77, 200, 255, 0.2);
  padding: 40px 0 20px;
  position: relative;
  overflow: hidden;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(77, 200, 255, 0.5),
    transparent
  );
  animation: footerGlow 3s ease-in-out infinite;
}

@keyframes footerGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.footer-brand {
  flex: 1;
}

.footer-logo {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: 2px;
}

.logo-highlight {
  color: #4dc8ff;
  text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);
}

.logo-normal {
  color: #ffffff;
  margin-left: 8px;
}

.footer-tagline {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  font-weight: 300;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.contact-email,
.contact-location {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-email:hover {
  color: #4dc8ff;
  cursor: pointer;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #4dc8ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-footer {
    padding: 30px 0 15px;
  }

  .footer-container {
    padding: 0 20px;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    margin-bottom: 20px;
  }

  .footer-contact {
    align-items: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .footer-links {
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-logo {
    font-size: 1.3rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 10px;
  }
}

/* 优化的地理数据可视化模块样式 - 突出大陆板块 */
.optimized-geo-visualization {
  position: relative;
  width: 100%;
  min-height: 75vh;
  background: transparent; /* 移除背景，使用父容器背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px; /* 恢复正常padding */
  overflow: hidden;
  border: none; /* 移除边框避免视觉分割 */
  margin: 0; /* 移除负边距，恢复正常布局 */
}

.optimized-geo-visualization::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(0, 229, 255, 0.02), transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255, 107, 107, 0.01), transparent 50%);
  z-index: 1;
  pointer-events: none;
}

.optimized-geo-visualization.loading {
  min-height: 40vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: #ffffff;
  z-index: 2;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(77, 200, 255, 0.3);
  border-top: 3px solid #4dc8ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.geo-viz-container {
  position: relative;
  max-width: 1600px;
  width: 100%;
  background: transparent; /* 完全透明背景，与整体界面融合 */
  backdrop-filter: none; /* 移除模糊效果 */
  border: 1px solid rgba(77, 200, 255, 0.08); /* 极淡的边框 */
  border-radius: 24px;
  padding: 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 简化阴影 */
  z-index: 2;
}

.geo-viz-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
}

.geo-viz-title {
  font-family: 'Orbitron', monospace;
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 10px 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  background: linear-gradient(45deg, #00e5ff, #4dc8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.geo-viz-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  letter-spacing: 0.5px;
}

.svg-map-container {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(135deg, #010313 0%, #010313 100%);
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 30px;
  border: 1px solid rgba(77, 200, 255, 0.08);
  box-shadow:
    inset 0 2px 15px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(77, 200, 255, 0.05);
}

.world-map-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.land-path {
  fill: url(#landGradient);
  stroke: rgba(77, 200, 255, 0.2);
  stroke-width: 0.3;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
}

.country-path {
  transition: all 0.5s ease;
  cursor: pointer;
}

.country-path.active {
  filter: drop-shadow(0 0 8px #00e5ff);
}

.activity-pulse {
  animation: activityPulse 2s ease-in-out infinite;
}

@keyframes activityPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.5);
  }
}

.connection-line {
  animation: connectionFlow 3s ease-in-out infinite;
}

@keyframes connectionFlow {
  0%, 100% {
    stroke-dasharray: 0, 100;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 50, 50;
    stroke-dashoffset: -25;
  }
}

/* 简洁的数据展示层样式 */
.data-overlay-clean {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  pointer-events: none;
}

.data-stats {
  display: flex;
  align-items: center;
  gap: 30px;
  background: rgba(1, 3, 19, 0.85);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 50px;
  padding: 15px 30px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 80px;
}

.stat-value {
  font-family: 'Orbitron', monospace;
  font-size: 1.4rem;
  font-weight: 700;
  color: #00e5ff;
  line-height: 1;
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
}

.stat-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(77, 200, 255, 0.5),
    transparent
  );
}

/* 技术能力展示区域样式 */
.tech-capabilities-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid rgba(77, 200, 255, 0.15);
  gap: 20px;
}

.capability-indicators {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.capability-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.capability-item:hover {
  color: #4dc8ff;
  transform: translateY(-2px);
}

.capability-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 技术图标样式 */
.tech-hexagon {
  width: 24px;
  height: 24px;
  background: linear-gradient(45deg, #4dc8ff, #74b9ff);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  animation: techRotate 4s linear infinite;
  box-shadow: 0 0 15px rgba(77, 200, 255, 0.5);
}

.tech-circuit {
  width: 20px;
  height: 20px;
  border: 2px solid #4dc8ff;
  border-radius: 4px;
  position: relative;
  animation: techPulse 2s ease-in-out infinite;
}

.tech-circuit::before,
.tech-circuit::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 2px;
  background: #4dc8ff;
  top: 50%;
  transform: translateY(-50%);
}

.tech-circuit::before {
  left: -8px;
}

.tech-circuit::after {
  right: -8px;
}

.tech-network {
  width: 20px;
  height: 20px;
  position: relative;
}

.tech-network::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, #4dc8ff 2px, transparent 2px),
              radial-gradient(circle at 0 0, #4dc8ff 1px, transparent 1px),
              radial-gradient(circle at 100% 0, #4dc8ff 1px, transparent 1px),
              radial-gradient(circle at 0 100%, #4dc8ff 1px, transparent 1px),
              radial-gradient(circle at 100% 100%, #4dc8ff 1px, transparent 1px);
  background-size: 20px 20px, 10px 10px, 10px 10px, 10px 10px, 10px 10px;
  background-position: center, top left, top right, bottom left, bottom right;
  animation: techNetwork 3s ease-in-out infinite;
}

@keyframes techRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes techPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(77, 200, 255, 0.5);
    border-color: #4dc8ff;
  }
  50% {
    box-shadow: 0 0 20px rgba(77, 200, 255, 0.8);
    border-color: #74b9ff;
  }
}

@keyframes techNetwork {
  0%, 100% {
    opacity: 0.7;
    filter: brightness(1);
  }
  50% {
    opacity: 1;
    filter: brightness(1.3);
  }
}

.tech-signature {
  text-align: center;
  margin-top: 10px;
}

.signature-text {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Titillium Web', sans-serif;
  font-size: 0.85rem;
  font-style: italic;
  letter-spacing: 0.5px;
  position: relative;
}

.signature-text::before,
.signature-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.5), transparent);
}

.signature-text::before {
  left: -40px;
}

.signature-text::after {
  right: -40px;
}

/* 高亮国家和科技效果样式 */
.country-path.highlighted {
  stroke: rgba(0, 229, 255, 0.8);
  stroke-width: 0.5;
  filter: drop-shadow(0 0 3px rgba(0, 229, 255, 0.6));
  animation: countryGlow 2s ease-in-out infinite alternate;
}

.tech-pulse {
  animation: techPulseEffect 2s ease-in-out infinite;
}

.tech-core {
  animation: techCoreEffect 1.5s ease-in-out infinite;
}

@keyframes countryGlow {
  0% {
    stroke: rgba(0, 229, 255, 0.6);
    filter: drop-shadow(0 0 2px rgba(0, 229, 255, 0.4));
  }
  100% {
    stroke: rgba(0, 229, 255, 1);
    filter: drop-shadow(0 0 5px rgba(0, 229, 255, 0.8));
  }
}

@keyframes techPulseEffect {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes techCoreEffect {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

/* 连接线动画效果 */
.connection-line {
  stroke-dasharray: 5, 5;
  animation: connectionFlow 3s linear infinite;
}

@keyframes connectionFlow {
  0% {
    stroke-dashoffset: 0;
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    stroke-dashoffset: 20;
    opacity: 0.3;
  }
}

/* 优化的地理数据可视化模块响应式样式 */
@media (max-width: 1024px) {
  .optimized-geo-visualization {
    padding: 50px 30px;
  }

  .geo-viz-container {
    padding: 35px 25px;
  }

  .svg-map-container {
    height: 400px;
  }

  .geo-viz-title {
    font-size: 1.8rem;
  }

  .data-stats {
    gap: 20px;
    padding: 12px 25px;
  }

  .stat-value {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .optimized-geo-visualization {
    padding: 40px 20px;
    min-height: 50vh;
  }

  .geo-viz-container {
    padding: 30px 20px;
  }

  .geo-viz-title {
    font-size: 1.5rem;
  }

  .geo-viz-subtitle {
    font-size: 1rem;
  }

  .svg-map-container {
    height: 350px;
  }

  .tech-capabilities-footer {
    gap: 15px;
  }

  .capability-indicators {
    gap: 25px;
    justify-content: center;
  }

  .data-stats {
    gap: 15px;
    padding: 10px 20px;
    flex-direction: column;
  }

  .stat-item {
    min-width: 60px;
  }

  .stat-value {
    font-size: 1.1rem;
  }

  .stat-label {
    font-size: 0.65rem;
  }

  .stat-divider {
    width: 60px;
    height: 1px;
    background: linear-gradient(
      to right,
      transparent,
      rgba(77, 200, 255, 0.5),
      transparent
    );
  }
}

@media (max-width: 480px) {
  .optimized-geo-visualization {
    padding: 30px 15px; /* 移动端保持适当padding */
  }

  .geo-viz-container {
    padding: 25px 15px;
  }

  .geo-viz-title {
    font-size: 1.3rem;
  }

  .geo-viz-subtitle {
    font-size: 0.9rem;
  }

  .svg-map-container {
    height: 300px;
  }

  .data-stats {
    gap: 10px;
    padding: 8px 15px;
    flex-direction: column;
  }

  .stat-value {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.6rem;
  }

  .capability-indicators {
    flex-direction: column;
    gap: 15px;
  }

  .capability-item {
    font-size: 0.8rem;
  }

  .signature-text {
    font-size: 0.75rem;
  }
}

/* 移除重复的地理数据可视化模块样式定义 - 已在上方统一定义 */

.geo-viz-container {
  position: relative;
  width: 100%;
  padding: 30px;
}

.geo-viz-header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 5;
}

.geo-viz-title {
  font-size: 28px;
  font-weight: 700;
  color: #00e5ff;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow: 0 0 15px rgba(0, 229, 255, 0.6);
  font-family: 'Orbitron', sans-serif;
  text-align: center;
}

/* SVG地图容器 */
.svg-map-container {
  position: relative;
  width: 100%;
  height: 420px;
  overflow: hidden;
  border-radius: 12px;
  background: #010313;
  border: 1px solid rgba(77, 200, 255, 0.3);
  box-shadow: inset 0 0 40px rgba(0, 0, 0, 0.4);
}

/* 技术背景网格 */
.tech-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(20, 80, 120, 0.04) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(20, 80, 120, 0.04) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: 1;
  opacity: 0.5;
}

/* 世界地图SVG */
.world-map-svg {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* 地图路径样式 */
.land-path {
  transition: fill 0.5s ease;
  filter: drop-shadow(0 0 3px rgba(36, 164, 216, 0.4));
}

.country-path {
  transition: all 0.3s ease;
}

.country-path.highlighted {
  animation: glowPulse 3s infinite alternate;
}

.country-pulse {
  transition: all 0.2s ease;
}

/* 热点样式 */
.industrial-hotspot {
  transition: all 0.3s ease;
}

.hotspot-dot {
  filter: drop-shadow(0 0 4px rgba(0, 229, 255, 0.8));
}

.hotspot-pulse {
  transition: all 0.1s ease;
}

.hotspot-label {
  font-family: 'Roboto Mono', monospace;
  font-size: 9px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  transition: opacity 0.3s ease;
  font-weight: 600;
  pointer-events: none;
  fill: #ffffff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
}

/* 交互式指示器 */
.interaction-indicator {
  position: absolute;
  bottom: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 20, 40, 0.7);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  z-index: 10;
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.2);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ffaa;
  animation: pulseIndicator 2s infinite;
}

.indicator-text {
  font-size: 10px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 1px;
  font-family: 'Roboto Mono', monospace;
}

/* 加载状态 */
.optimized-geo-visualization.loading {
  height: 420px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container {
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 229, 255, 0.3);
  border-top: 3px solid rgba(0, 229, 255, 1);
  border-radius: 50%;
  margin: 0 auto 15px;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes glowPulse {
  0% {
    filter: drop-shadow(0 0 1px rgba(0, 229, 255, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 8px rgba(0, 229, 255, 0.8));
  }
}

@keyframes pulseIndicator {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 255, 170, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 地图悬停效果 */
.svg-map-container:hover .hotspot-dot {
  filter: drop-shadow(0 0 6px rgba(0, 229, 255, 1));
}

.svg-map-container:hover .data-packet {
  filter: drop-shadow(0 0 6px rgba(0, 255, 170, 1));
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .optimized-geo-visualization {
    margin: 40px auto;
  }

  .svg-map-container {
    height: 380px;
  }
}

@media (max-width: 992px) {
  .geo-viz-title {
    font-size: 24px;
  }

  .svg-map-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .optimized-geo-visualization {
    margin: 0; /* 移动端完全移除margin */
    padding: 40px 20px; /* 调整移动端padding */
  }

  .geo-viz-container {
    padding: 20px;
  }

  .geo-viz-title {
    font-size: 20px;
    letter-spacing: 2px;
  }

  .svg-map-container {
    height: 320px;
  }
}

@media (max-width: 576px) {
  .geo-viz-container {
    padding: 15px;
  }

  .geo-viz-title {
    font-size: 18px;
    letter-spacing: 1px;
  }

  .svg-map-container {
    height: 280px;
  }
}

/* 全球工业网络地图组件 - 重构版：移除背景，专注内容 */
.geo-visualization {
  position: relative;
  width: 100%;
  padding: 60px 10% 80px 10%;
  background: transparent; /* 移除背景，使用全局背景 */
  border: none;
  margin: 0;
  overflow: hidden;
}

.geo-visualization.loading {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.geo-visualization .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.geo-visualization .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 229, 255, 0.2);
  border-top: 3px solid rgba(0, 229, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.geo-viz-title {
  font-size: 2.4rem;
  font-weight: 600;
  text-align: center;
  margin: 0 0 30px 0;
  color: #ffffff;
  letter-spacing: 3px;
  text-transform: uppercase;
  font-family: 'Orbitron', sans-serif;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);
}

.world-map-container {
  position: relative;
  width: 100%;
  max-width: 1400px; /* 增大最大宽度 */
  height: 600px; /* 大幅增大高度 */
  margin: 0 auto; /* 居中显示 */
  overflow: hidden;
  background: transparent; /* 移除蓝紫色背景，使用透明背景 */
  border-radius: 12px; /* 增大圆角 */
  border: 1px solid rgba(77, 200, 255, 0.1); /* 减弱边框，更加优雅 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); /* 简化阴影效果 */
}

.world-map-svg {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.land-path {
  transition: fill 0.3s ease;
}

.country-path {
  transition: all 0.3s ease;
}

.country-path.highlighted {
  fill: rgba(0, 229, 255, 0.15);
}

.industrial-hotspot {
  cursor: pointer;
  transition: all 0.3s ease;
}

.hotspot-dot {
  transition: all 0.3s ease;
}

.hotspot-pulse {
  transition: all 0.1s ease;
}

.hotspot-label {
  font-family: 'Orbitron', monospace;
  font-size: 9px;
  letter-spacing: 1px;
  text-transform: uppercase;
  pointer-events: none;
  transition: all 0.3s ease;
}

.interaction-indicator {
  position: absolute;
  bottom: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 20px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.2);
}

.indicator-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #00ffaa;
  margin-right: 8px;
  /* Animation disabled for performance optimization */
}

.indicator-text {
  color: #ffffff;
  font-size: 10px;
  font-family: 'Orbitron', monospace;
  letter-spacing: 1px;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.5);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计优化 */
@media (max-width: 1400px) {
  .geo-visualization {
    padding: 60px 8% 80px 8%;
  }

  .world-map-container {
    max-width: 1200px;
    height: 550px;
  }

  .geo-viz-title {
    font-size: 2.2rem;
    letter-spacing: 2.5px;
  }
}

@media (max-width: 1200px) {
  .geo-visualization {
    padding: 50px 6% 70px 6%;
  }

  .world-map-container {
    max-width: 1000px;
    height: 500px;
  }

  .geo-viz-title {
    font-size: 2.0rem;
    letter-spacing: 2px;
  }
}

@media (max-width: 992px) {
  .geo-visualization {
    padding: 40px 4% 60px 4%;
  }

  .world-map-container {
    max-width: 100%;
    height: 450px;
  }

  .geo-viz-title {
    font-size: 1.8rem;
    letter-spacing: 2px;
  }
}

@media (max-width: 768px) {
  .geo-visualization {
    padding: 30px 2% 50px 2%;
  }

  .world-map-container {
    height: 380px;
    border-radius: 8px;
  }

  .geo-viz-title {
    font-size: 1.6rem;
    letter-spacing: 1.5px;
  }
}

@media (max-width: 576px) {
  .geo-visualization {
    padding: 20px 1% 40px 1%;
  }

  .world-map-container {
    height: 320px;
    border-radius: 6px;
  }

  .geo-viz-title {
    font-size: 1.4rem;
    letter-spacing: 1px;
    margin: 0 0 20px 0;
  }
}

/* 用户菜单样式 */
.user-menu-wrapper {
  position: relative;
  /* 确保下拉菜单可以正确定位 */
  overflow: visible;
}

.user-menu-button {
  display: flex;
  align-items: center;
  padding: 8px 14px;
  background: transparent;
  border: none;
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 2;
  /* 确保用户菜单按钮高度固定 */
  height: 32px;
  box-sizing: border-box;

  /* 用户菜单按钮的折射效果 */
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.3),
    0 0 20px rgba(255, 255, 255, 0.2),
    0 0 30px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.4),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.2),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

.user-menu-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.user-menu-button:hover {
  color: #ffffff;
  background: rgba(77, 200, 255, 0.1);
  /* 移除所有变换效果 */
  transform: none;

  /* 用户菜单按钮悬停时的增强折射效果 */
  text-shadow:
    0 0 15px rgba(255, 255, 255, 0.5),
    0 0 25px rgba(255, 255, 255, 0.3),
    0 0 35px rgba(77, 200, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.6),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.3),
    -1px 0 0 rgba(0, 0, 0, 0.2),
    0 0 5px rgba(77, 200, 255, 0.4);

  filter:
    drop-shadow(0 0 3px rgba(255, 255, 255, 0.4))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(77, 200, 255, 0.3));
}

.user-menu-button:hover::before {
  left: 100%;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00a8ff, #0065ff);
  color: white;
  font-weight: bold;
  margin-right: 8px;
  font-size: 14px;
}

.user-name {
  margin: 0 8px;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-dropdown {
  position: fixed;
  width: 180px;
  background: rgba(1, 3, 19, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: visible;
  z-index: 1200;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: dropdownFadeIn 0.2s ease;
  /* 防止被截断 */
  will-change: transform, opacity;
  /* 确保在所有容器之上 */
  isolation: isolate;
  /* 防止被父容器样式影响 */
  pointer-events: auto;
  margin: 0;
  padding: 8px;
}

.user-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-align: left;
  transition: all 0.3s ease;
  width: 100%;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 6px;

  /* 用户下拉菜单项的折射效果 */
  text-shadow:
    0 0 8px rgba(255, 255, 255, 0.2),
    0 0 15px rgba(255, 255, 255, 0.15),
    0 0 25px rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.3),
    0 -1px 0 rgba(0, 0, 0, 0.2),
    1px 0 0 rgba(255, 255, 255, 0.15),
    -1px 0 0 rgba(0, 0, 0, 0.1);

  filter:
    drop-shadow(0 0 1px rgba(255, 255, 255, 0.2))
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
}

.user-dropdown .dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;

  /* 用户下拉菜单项悬停时的增强折射效果 */
  text-shadow:
    0 0 12px rgba(255, 255, 255, 0.4),
    0 0 20px rgba(255, 255, 255, 0.25),
    0 0 30px rgba(77, 200, 255, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.5),
    0 -1px 0 rgba(0, 0, 0, 0.3),
    1px 0 0 rgba(255, 255, 255, 0.25),
    -1px 0 0 rgba(0, 0, 0, 0.15),
    0 0 4px rgba(77, 200, 255, 0.3);

  filter:
    drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))
    drop-shadow(0 1px 2px rgba(0, 0, 0, 0.25))
    drop-shadow(0 0 6px rgba(77, 200, 255, 0.25));
}

.user-dropdown .dropdown-item svg {
  margin-right: 12px;
  opacity: 0.7;
}

.user-dropdown .dropdown-item:hover svg {
  opacity: 1;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-name {
    max-width: 80px;
  }
}

@media (max-width: 480px) {
  .user-name {
    display: none;
  }

  .user-avatar {
    margin-right: 0;
  }
}

/* 信息展示区域 */
.information-panel-section {
  position: relative;
  width: 55%;
  display: flex;
  flex-direction: column;
  z-index: 15;
}

/* 主信息面板 */
.main-info-panel {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, 
    rgba(5, 15, 35, 0.85) 0%,
    rgba(10, 25, 50, 0.9) 50%,
    rgba(5, 15, 35, 0.85) 100%);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(100, 200, 255, 0.15);
  padding: 50px 45px;
}

/* 面板装饰框架 */
.panel-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.frame-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 220, 255, 0.6);
}

.corner-tl {
  top: 15px;
  left: 15px;
  border-right: none;
  border-bottom: none;
}

.corner-tr {
  top: 15px;
  right: 15px;
  border-left: none;
  border-bottom: none;
}

.corner-bl {
  bottom: 15px;
  left: 15px;
  border-right: none;
  border-top: none;
}

.corner-br {
  bottom: 15px;
  right: 15px;
  border-left: none;
  border-top: none;
}

.frame-border {
  position: absolute;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 200, 255, 0.3) 50%, 
    transparent 100%);
  opacity: 0;
  animation: borderScan 6s infinite ease-in-out;
}

.border-top {
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  animation-delay: 0s;
}

.border-right {
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgba(0, 200, 255, 0.3) 50%, 
    transparent 100%);
  animation-delay: 1.5s;
}

.border-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  animation-delay: 3s;
}

.border-left {
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgba(0, 200, 255, 0.3) 50%, 
    transparent 100%);
  animation-delay: 4.5s;
}

@keyframes borderScan {
  0%, 90% { opacity: 0; }
  5%, 15% { opacity: 1; }
}

/* 状态指示器 */
.status-indicators {
  position: absolute;
  top: 20px;
  right: 25px;
  display: flex;
  gap: 8px;
  z-index: 2;
}

.status-led {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.led-active {
  background: #00ff88;
  animation: ledPulse 2s infinite ease-in-out;
}

.led-standby {
  background: #ffaa00;
  animation: ledPulse 2s infinite ease-in-out 0.7s;
}

.led-processing {
  background: #0088ff;
  animation: ledPulse 2s infinite ease-in-out 1.4s;
}

@keyframes ledPulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

/* 面板内容 */
.panel-content {
  position: relative;
  z-index: 2;
}

/* 系统标识 */
.system-identifier {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(100, 200, 255, 0.2);
}

.sys-code {
  font-family: 'Orbitron', monospace;
  font-size: 12px;
  color: rgba(0, 220, 255, 0.8);
  letter-spacing: 1px;
  font-weight: 500;
}

.sys-status {
  font-family: 'Orbitron', monospace;
  font-size: 10px;
  color: #00ff88;
  letter-spacing: 0.5px;
  background: rgba(0, 255, 136, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

/* 增强标题区域 */
.enhanced-title-section {
  margin-bottom: 35px;
}

.title-prefix {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.prefix-text {
  font-family: 'Orbitron', monospace;
  font-size: 11px;
  color: rgba(100, 200, 255, 0.7);
  letter-spacing: 2px;
  font-weight: 400;
}

.prefix-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, 
    rgba(100, 200, 255, 0.4) 0%, 
    transparent 100%);
}

.main-heading {
  margin: 0;
  font-family: 'Rajdhani', sans-serif;
  line-height: 1.1;
}

.heading-line {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
  overflow: hidden;
}

.word-block {
  font-size: 3.2rem;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 1px;
  opacity: 0;
  transform: translateY(30px);
  animation: wordReveal 0.8s ease-out forwards;
}

.word-block.accent {
  color: rgba(150, 200, 255, 0.9);
  animation-delay: 0.1s;
}

.word-block.highlight {
  color: #00e5ff;
  text-shadow: 0 0 20px rgba(0, 229, 255, 0.5);
  animation-delay: 0.2s;
}

.word-block.success {
  background: linear-gradient(135deg, #00ff88, #00e5ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation-delay: 0.3s;
}

@keyframes wordReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.title-underline {
  display: flex;
  gap: 4px;
  margin-top: 15px;
}

.underline-segment {
  height: 3px;
  background: linear-gradient(90deg, 
    rgba(0, 229, 255, 0.8) 0%, 
    rgba(0, 180, 255, 0.4) 100%);
  border-radius: 2px;
  transform: scaleX(0);
  transform-origin: left;
  animation: segmentGrow 0.6s ease-out forwards;
}

.underline-segment:nth-child(1) {
  width: 80px;
  animation-delay: 0.5s;
}

.underline-segment:nth-child(2) {
  width: 60px;
  animation-delay: 0.7s;
}

.underline-segment:nth-child(3) {
  width: 40px;
  animation-delay: 0.9s;
}

@keyframes segmentGrow {
  to { transform: scaleX(1); }
}

/* 描述内容区域 */
.description-section {
  margin-bottom: 40px;
}

.description-primary {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 50, 100, 0.15);
  border-radius: 12px;
  border-left: 3px solid rgba(0, 220, 255, 0.4);
}

.desc-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  color: rgba(0, 220, 255, 0.8);
  margin-top: 2px;
}

.description-primary p {
  margin: 0;
  font-family: 'Titillium Web', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
}

.description-secondary {
  margin-bottom: 20px;
}

.feature-highlights {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Titillium Web', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0;
  transform: translateX(-20px);
  animation: featureSlideIn 0.6s ease-out forwards;
}

.feature-item:nth-child(1) { animation-delay: 1.2s; }
.feature-item:nth-child(2) { animation-delay: 1.4s; }
.feature-item:nth-child(3) { animation-delay: 1.6s; }

.feature-dot {
  width: 6px;
  height: 6px;
  background: rgba(0, 220, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 220, 255, 0.4);
  flex-shrink: 0;
}

@keyframes featureSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sub-description {
  font-family: 'Titillium Web', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.65);
  margin: 0;
  font-style: italic;
}

/* 操作区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.cta-container {
  display: flex;
  justify-content: flex-start;
}

.enhanced-cta-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, 
    rgba(0, 150, 255, 0.2) 0%, 
    rgba(0, 200, 255, 0.3) 100%);
  border: 2px solid rgba(0, 220, 255, 0.4);
  border-radius: 12px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  padding: 16px 36px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.enhanced-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    transparent 100%);
  transition: left 0.6s ease;
}

.enhanced-cta-button:hover {
  background: linear-gradient(135deg, 
    rgba(0, 180, 255, 0.3) 0%, 
    rgba(0, 220, 255, 0.4) 100%);
  border-color: rgba(0, 255, 200, 0.6);
  box-shadow: 
    0 8px 25px rgba(0, 200, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.enhanced-cta-button:hover::before {
  left: 100%;
}

.btn-icon {
  color: rgba(0, 220, 255, 0.8);
}

.btn-text {
  flex: 1;
}

.btn-arrow {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.enhanced-cta-button:hover .btn-arrow {
  transform: translateX(4px);
}

.additional-info {
  display: flex;
  gap: 30px;
}

.info-line {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-family: 'Titillium Web', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

.info-value {
  font-family: 'Orbitron', monospace;
  font-size: 14px;
  color: #00ff88;
  font-weight: 600;
}

/* 侧边数据流 */
.side-data-streams {
  position: absolute;
  top: 0;
  right: -30px;
  bottom: 0;
  width: 2px;
  z-index: 1;
}

.data-stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgba(0, 220, 255, 0.8) 50%, 
    transparent 100%);
  opacity: 0;
  animation: streamFlow 3s infinite ease-in-out;
}

.stream-1 {
  animation-delay: 0s;
}

.stream-2 {
  animation-delay: 1s;
}

.stream-3 {
  animation-delay: 2s;
}

@keyframes streamFlow {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh - 100px));
    opacity: 0;
  }
}

/* 环境装饰元素 */
.environmental-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 3;
}

.energy-pulse {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, 
    rgba(0, 255, 200, 1) 0%, 
    rgba(0, 200, 255, 0.5) 70%, 
    transparent 100%);
  border-radius: 50%;
  animation: energyPulseMove 8s infinite linear;
}

.pulse-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.pulse-2 {
  bottom: 30%;
  right: 25%;
  animation-delay: 4s;
}

@keyframes energyPulseMove {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(200px, 100px) scale(0.5);
    opacity: 0;
  }
}

.data-packets {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.data-packet {
  position: absolute;
  width: 3px;
  height: 12px;
  background: linear-gradient(180deg, 
    rgba(0, 220, 255, 0.8) 0%, 
    rgba(100, 200, 255, 0.4) 100%);
  border-radius: 2px;
  opacity: 0;
  animation: packetTravel 6s infinite ease-in-out;
}

.packet-1 {
  top: 25%;
  left: 30%;
  animation-delay: 0s;
}

.packet-2 {
  top: 50%;
  left: 60%;
  animation-delay: 2s;
}

.packet-3 {
  top: 75%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes packetTravel {
  0% {
    transform: translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateX(300px);
    opacity: 0;
  }
}

/* 响应式设计 */
@media screen and (max-width: 1400px) {
  .industrial-module-container {
    gap: 60px;
  }
  
  .word-block {
    font-size: 2.8rem;
    padding: 8px 16px;
  }
  
  .enhanced-cta-button {
    padding: 12px 28px;
    font-size: 15px;
  }
}

@media screen and (max-width: 1200px) {
  .information-panel-section {
    padding: 80px 20px;
  }
  
  .main-info-panel {
    padding: 40px 30px;
    max-width: 90%;
  }
  
  .word-block {
    font-size: 2.4rem;
    padding: 6px 14px;
  }
  
  .enhanced-title-section {
    margin-bottom: 25px;
  }
  
  .description-primary {
    font-size: 16px;
    line-height: 1.6;
  }
}

@media screen and (max-width: 992px) {
  .information-panel-section {
    padding: 60px 15px;
  }
  
  .main-info-panel {
    padding: 35px 25px;
    max-width: 95%;
  }
  
  .word-block {
    font-size: 2rem;
    padding: 5px 12px;
    margin: 0 4px 8px 0;
  }
  
  .title-prefix {
    font-size: 13px;
  }
  
  .description-primary {
    font-size: 15px;
    margin-bottom: 20px;
  }
  
  .feature-highlights {
    margin: 20px 0;
  }
  
  .feature-item {
    font-size: 13px;
    margin: 8px 0;
  }
  
  .enhanced-cta-button {
    padding: 10px 24px;
    font-size: 14px;
  }
}

@media screen and (max-width: 768px) {
  .information-panel-section {
    padding: 50px 10px;
  }
  
  .main-info-panel {
    padding: 30px 20px;
    margin: 0 10px;
  }
  
  .word-block {
    font-size: 1.8rem;
    padding: 4px 10px;
    margin: 0 3px 6px 0;
  }
  
  .title-prefix {
    font-size: 12px;
    margin-bottom: 15px;
  }
  
  .prefix-text {
    font-size: 12px;
  }
  
  .description-primary {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 18px;
  }
  
  .description-secondary {
    font-size: 13px;
    margin-bottom: 20px;
  }
  
  .feature-item {
    font-size: 12px;
    margin: 6px 0;
  }
  
  .enhanced-cta-button {
    padding: 9px 20px;
    font-size: 13px;
    margin-top: 20px;
  }
  
  .btn-text {
    margin-right: 8px;
  }
  
  .btn-arrow {
    font-size: 16px;
  }
  
  .status-indicators {
    gap: 8px;
  }
  
  .status-led {
    width: 8px;
    height: 8px;
  }
}

@media screen and (max-width: 576px) {
  .information-panel-section {
    padding: 40px 8px;
  }
  
  .main-info-panel {
    padding: 25px 16px;
    margin: 0 5px;
  }
  
  .word-block {
    font-size: 1.5rem;
    padding: 3px 8px;
    margin: 0 2px 5px 0;
  }
  
  .title-prefix {
    font-size: 11px;
    margin-bottom: 12px;
  }
  
  .prefix-text {
    font-size: 11px;
  }
  
  .description-primary {
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 16px;
  }
  
  .description-secondary {
    font-size: 12px;
    margin-bottom: 18px;
  }
  
  .feature-item {
    font-size: 11px;
    margin: 5px 0;
  }
  
  .enhanced-cta-button {
    padding: 8px 16px;
    font-size: 12px;
    margin-top: 18px;
  }
  
  .btn-text {
    margin-right: 6px;
  }
  
  .btn-arrow {
    font-size: 14px;
  }
  
  .system-identifier {
    font-size: 11px;
  }
  
  .sys-code {
    font-size: 11px;
  }
  
  .sys-status {
    font-size: 10px;
  }
}

@media screen and (max-width: 480px) {
  .information-panel-section {
    padding: 35px 6px;
  }
  
  .main-info-panel {
    padding: 20px 14px;
    margin: 0 3px;
  }
  
  .word-block {
    font-size: 1.3rem;
    padding: 2px 6px;
    margin: 0 1px 4px 0;
  }
  
  .enhanced-title-section {
    margin-bottom: 15px;
  }
  
  .description-primary {
    font-size: 12px;
    margin-bottom: 14px;
  }
  
  .description-secondary {
    font-size: 11px;
    margin-bottom: 16px;
  }
  
  .feature-item {
    font-size: 10px;
    margin: 4px 0;
  }
  
  .enhanced-cta-button {
    padding: 7px 14px;
    font-size: 11px;
    margin-top: 16px;
  }
  
  .additional-info {
    gap: 15px;
    margin-top: 15px;
  }
  
  .info-label {
    font-size: 10px;
  }
  
  .info-value {
    font-size: 12px;
  }
}

@media screen and (max-width: 360px) {
  .main-info-panel {
    padding: 18px 12px;
    margin: 0 2px;
  }
  
  .word-block {
    font-size: 1.2rem;
    padding: 2px 5px;
    margin: 0 1px 3px 0;
  }
  
  .description-primary {
    font-size: 11px;
    margin-bottom: 12px;
  }
  
  .enhanced-cta-button {
    padding: 6px 12px;
    font-size: 10px;
  }
  
  .additional-info {
    gap: 12px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-line {
    gap: 6px;
  }
}

/* 高对比度和可访问性 */
@media (prefers-contrast: high) {
  .word-block {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
  }
  
  .enhanced-cta-button {
    background: #000000;
    color: #ffffff;
    border: 2px solid #ffffff;
  }
  
  .description-primary,
  .description-secondary {
    color: #ffffff;
    text-shadow: 1px 1px 2px #000000;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .word-block,
  .enhanced-cta-button,
  .status-led,
  .feature-item {
    animation: none;
  }
  
  .enhanced-cta-button::before {
    animation: none;
  }
  
  .btn-arrow {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .information-panel-section {
    background: white;
    color: black;
    padding: 20px;
  }
  
  .main-info-panel {
    box-shadow: none;
    border: 1px solid #000000;
    background: white;
  }
  
  .word-block {
    background: white;
    color: black;
    border: 1px solid #000000;
  }
  
  .enhanced-cta-button {
    display: none;
  }
  
  .status-indicators,
  .panel-frame {
    display: none;
  }
}

/* MagicBento 联系我们邀请模块样式 - 性能优化版本 */
.contact-invitation-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #010313 0%, #010313 50%, #010313 100%);
  position: relative;
  overflow: hidden;
  min-height: 600px;
}

/* 过渡装饰元素 */
.section-transition {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0 3rem;
  opacity: 0.6;
}

.transition-line {
  height: 1px;
  width: 100px;
  background: linear-gradient(90deg, transparent, rgba(132, 0, 255, 0.5), transparent);
}

.transition-dots {
  display: flex;
  gap: 8px;
  margin: 0 20px;
}

.transition-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(132, 0, 255, 0.7);
  animation: pulse-dot 2s infinite;
}

.transition-dots .dot:nth-child(2) {
  animation-delay: 0.3s;
}

.transition-dots .dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.contact-invitation-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 40%, rgba(0, 168, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(0, 255, 150, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.contact-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.contact-header {
  margin-bottom: 50px;
}

.contact-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  line-height: 1.2;
  text-shadow: 0 0 20px rgba(0, 168, 255, 0.3);
}

.contact-subtitle {
  font-size: 1.2rem;
  color: #b0b8c4;
  line-height: 1.6;
  font-weight: 300;
}

.contact-features {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.contact-features .feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  min-width: 150px;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.contact-features .feature-item:nth-child(1) { animation-delay: 0.2s; }
.contact-features .feature-item:nth-child(2) { animation-delay: 0.4s; }
.contact-features .feature-item:nth-child(3) { animation-delay: 0.6s; }

.contact-features .feature-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #00a8ff, #0078d4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.contact-features .feature-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 168, 255, 0.3);
}

.contact-features .feature-item span {
  color: #e1e5e9;
  font-size: 0.95rem;
  font-weight: 500;
}

.contact-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.primary-cta-btn {
  background: linear-gradient(135deg, #00a8ff, #0078d4);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 200px;
  justify-content: center;
}

.primary-cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 168, 255, 0.4);
  background: linear-gradient(135deg, #0078d4, #00a8ff);
}

.primary-cta-btn svg {
  transition: transform 0.3s ease;
}

.primary-cta-btn:hover svg {
  transform: translateX(4px);
}

.secondary-cta-btn {
  background: transparent;
  color: #00a8ff;
  border: 2px solid #00a8ff;
  padding: 14px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 180px;
}

.secondary-cta-btn:hover {
  background: #00a8ff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 168, 255, 0.2);
}

.contact-info {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #b0b8c4;
  font-size: 0.95rem;
}

.info-item svg {
  color: #00a8ff;
  flex-shrink: 0;
}

.contact-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #00a8ff;
  font-family: 'Orbitron', sans-serif;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #8a92a0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-invitation-section {
    padding: 60px 0;
  }
  
  .contact-title {
    font-size: 2rem;
  }
  
  .contact-subtitle {
    font-size: 1.1rem;
  }
  
  .contact-features {
    gap: 30px;
    margin-bottom: 40px;
  }
  
  .contact-actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .primary-cta-btn,
  .secondary-cta-btn {
    width: 100%;
    max-width: 280px;
  }
  
  .contact-info {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .contact-stats {
    gap: 40px;
  }
}

@media (max-width: 480px) {
  .contact-invitation-section {
    padding: 40px 0;
  }
  
  .contact-title {
    font-size: 1.8rem;
  }
  
  .contact-subtitle {
    font-size: 1rem;
  }
  
  .contact-features {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .contact-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
}

/* 减少动画以提高性能 */
@media (prefers-reduced-motion: reduce) {
  .contact-features .feature-item {
    animation: none;
    opacity: 1;
  }
  
  .primary-cta-btn:hover,
  .secondary-cta-btn:hover,
  .contact-features .feature-icon:hover {
    transform: none;
  }
  
  .primary-cta-btn:hover svg {
    transform: none;
  }
}